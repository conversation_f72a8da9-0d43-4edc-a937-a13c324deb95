use std::fs;
use std::io::Write;
use chrono::Utc;
use log::{info, error, warn};

pub struct AppLogger;

impl AppLogger {
    pub fn init() -> Result<(), Box<dyn std::error::Error>> {
        // 创建日志目录 - 使用项目根目录
        let current_dir = std::env::current_dir().expect("Failed to get current directory");
        let project_root = current_dir.parent().unwrap_or(&current_dir);
        let log_dir = project_root.join("log");

        if !log_dir.exists() {
            fs::create_dir_all(&log_dir)?;
        }

        // 生成带时间戳的日志文件名
        let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
        let log_file = log_dir.join(format!("task-{}.log", timestamp));

        // 简化的日志配置 - 只输出到控制台
        env_logger::Builder::from_default_env()
            .format(|buf, record| {
                use std::io::Write;
                writeln!(
                    buf,
                    "{} [{}] {} - {}",
                    Utc::now().format("%Y-%m-%d %H:%M:%S%.3f"),
                    record.level(),
                    record.target(),
                    record.args()
                )
            })
            .init();

        // 记录日志文件路径到控制台
        println!("Application logger initialized, log file: {:?}", log_file);

        // 手动写入一些日志到文件
        let mut file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&log_file)?;

        writeln!(file, "{} [INFO] Application started", Utc::now().format("%Y-%m-%d %H:%M:%S%.3f"))?;
        writeln!(file, "{} [INFO] Logger initialized", Utc::now().format("%Y-%m-%d %H:%M:%S%.3f"))?;

        info!("Application logger initialized, log file: {:?}", log_file);
        Ok(())
    }

    pub fn log_operation(operation: &str, details: &str) {
        info!("OPERATION: {} - {}", operation, details);
    }

    #[allow(dead_code)]
    pub fn log_error(operation: &str, error: &str) {
        error!("ERROR: {} - {}", operation, error);
    }

    #[allow(dead_code)]
    pub fn log_warning(operation: &str, warning: &str) {
        warn!("WARNING: {} - {}", operation, warning);
    }

    #[allow(dead_code)]
    pub fn log_host_operation(action: &str, host_id: Option<i64>, host_name: &str, details: &str) {
        let id_str = host_id.map_or("NEW".to_string(), |id| id.to_string());
        info!("HOST_OP: {} - ID:{} Name:'{}' - {}", action, id_str, host_name, details);
    }

    #[allow(dead_code)]
    pub fn log_task_operation(action: &str, task_id: Option<i64>, task_name: &str, details: &str) {
        let id_str = task_id.map_or("NEW".to_string(), |id| id.to_string());
        info!("TASK_OP: {} - ID:{} Name:'{}' - {}", action, id_str, task_name, details);
    }

    #[allow(dead_code)]
    pub fn log_batch_operation(operation: &str, item_type: &str, count: usize, success_count: usize, details: &str) {
        info!("BATCH_OP: {} {} - Total:{} Success:{} Failed:{} - {}",
              operation, item_type, count, success_count, count - success_count, details);
    }

    #[allow(dead_code)]
    pub fn log_csv_operation(action: &str, file_size: usize, row_count: usize, success_count: usize, details: &str) {
        info!("CSV_OP: {} - FileSize:{}bytes Rows:{} Success:{} Failed:{} - {}",
              action, file_size, row_count, success_count, row_count - success_count, details);
    }

    #[allow(dead_code)]
    pub fn log_connection_test(host_id: i64, host_name: &str, hostname: &str, success: bool, details: &str) {
        if success {
            info!("CONN_TEST: SUCCESS - ID:{} Name:'{}' Host:{} - {}", host_id, host_name, hostname, details);
        } else {
            warn!("CONN_TEST: FAILED - ID:{} Name:'{}' Host:{} - {}", host_id, host_name, hostname, details);
        }
    }

    #[allow(dead_code)]
    pub fn log_task_execution(task_id: i64, task_name: &str, host_count: usize, status: &str, details: &str) {
        info!("TASK_EXEC: {} - ID:{} Name:'{}' Hosts:{} - {}", status, task_id, task_name, host_count, details);
    }
}

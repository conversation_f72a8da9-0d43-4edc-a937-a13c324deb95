use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::process::{Command, Stdio};
use std::io::{BufRead, BufReader, Write};
use tokio::sync::mpsc;
use uuid::Uuid;
use crate::models::{Host, SshTerminalSession};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TerminalOutput {
    pub session_id: String,
    pub output: String,
    pub output_type: String, // "stdout", "stderr", "info"
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TerminalCommand {
    pub session_id: String,
    pub command: String,
}

pub struct SshTerminalService {
    sessions: Arc<Mutex<HashMap<String, SshTerminalSession>>>,
    output_senders: Arc<Mutex<HashMap<String, mpsc::UnboundedSender<TerminalOutput>>>>,
}

impl SshTerminalService {
    pub fn new() -> Self {
        Self {
            sessions: Arc::new(Mutex::new(HashMap::new())),
            output_senders: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn create_session(&self, host: Host) -> Result<String, String> {
        let session_id = Uuid::new_v4().to_string();
        
        // 创建会话记录
        let session = SshTerminalSession {
            session_id: session_id.clone(),
            host_id: host.id,
            created_at: chrono::Utc::now().to_rfc3339(),
        };

        // 存储会话
        {
            let mut sessions = self.sessions.lock().unwrap();
            sessions.insert(session_id.clone(), session);
        }

        // 创建输出通道
        let (tx, mut rx) = mpsc::unbounded_channel::<TerminalOutput>();
        {
            let mut senders = self.output_senders.lock().unwrap();
            senders.insert(session_id.clone(), tx.clone());
        }

        // 启动SSH连接
        let session_id_clone = session_id.clone();
        let host_clone = host.clone();
        
        tokio::spawn(async move {
            if let Err(e) = Self::start_ssh_session(host_clone, session_id_clone.clone(), tx.clone()).await {
                let _ = tx.send(TerminalOutput {
                    session_id: session_id_clone,
                    output: format!("SSH连接失败: {}", e),
                    output_type: "stderr".to_string(),
                });
            }
        });

        Ok(session_id)
    }

    async fn start_ssh_session(
        host: Host, 
        session_id: String, 
        output_sender: mpsc::UnboundedSender<TerminalOutput>
    ) -> Result<(), String> {
        // 发送连接信息
        let _ = output_sender.send(TerminalOutput {
            session_id: session_id.clone(),
            output: format!("正在连接到 {}@{}:{}...\n", host.username, host.hostname, host.port),
            output_type: "info".to_string(),
        });

        // 构建SSH命令
        let mut ssh_args = vec![
            "-o", "StrictHostKeyChecking=no",
            "-o", "UserKnownHostsFile=/dev/null",
            "-p", &host.port.to_string(),
        ];

        // 如果有密码，使用sshpass
        let mut cmd = if let Some(password) = &host.password {
            let mut command = Command::new("sshpass");
            command.args(["-p", password]);
            command.arg("ssh");
            command.args(&ssh_args);
            command.arg(format!("{}@{}", host.username, host.hostname));
            command
        } else {
            // 使用密钥认证
            let mut command = Command::new("ssh");
            if let Some(_private_key) = &host.private_key {
                // TODO: 处理私钥文件
                ssh_args.extend(["-o", "PasswordAuthentication=no"]);
            }
            command.args(&ssh_args);
            command.arg(format!("{}@{}", host.username, host.hostname));
            command
        };

        // 设置为交互式终端
        cmd.stdin(Stdio::piped())
           .stdout(Stdio::piped())
           .stderr(Stdio::piped());

        let mut child = cmd.spawn().map_err(|e| format!("启动SSH进程失败: {}", e))?;

        // 获取stdin, stdout, stderr
        let stdin = child.stdin.take().ok_or("无法获取stdin")?;
        let stdout = child.stdout.take().ok_or("无法获取stdout")?;
        let stderr = child.stderr.take().ok_or("无法获取stderr")?;

        // 发送连接成功信息
        let _ = output_sender.send(TerminalOutput {
            session_id: session_id.clone(),
            output: format!("已连接到 {}@{}\n", host.username, host.hostname),
            output_type: "info".to_string(),
        });

        // 处理stdout
        let output_sender_stdout = output_sender.clone();
        let session_id_stdout = session_id.clone();
        tokio::spawn(async move {
            let reader = BufReader::new(stdout);
            for line in reader.lines() {
                if let Ok(line) = line {
                    let _ = output_sender_stdout.send(TerminalOutput {
                        session_id: session_id_stdout.clone(),
                        output: format!("{}\n", line),
                        output_type: "stdout".to_string(),
                    });
                }
            }
        });

        // 处理stderr
        let output_sender_stderr = output_sender.clone();
        let session_id_stderr = session_id.clone();
        tokio::spawn(async move {
            let reader = BufReader::new(stderr);
            for line in reader.lines() {
                if let Ok(line) = line {
                    let _ = output_sender_stderr.send(TerminalOutput {
                        session_id: session_id_stderr.clone(),
                        output: format!("{}\n", line),
                        output_type: "stderr".to_string(),
                    });
                }
            }
        });

        // 等待进程结束
        let _ = child.wait();
        
        let _ = output_sender.send(TerminalOutput {
            session_id,
            output: "SSH连接已断开\n".to_string(),
            output_type: "info".to_string(),
        });

        Ok(())
    }

    pub async fn send_command(&self, session_id: &str, command: &str) -> Result<(), String> {
        // TODO: 实现命令发送到SSH会话
        // 这需要维护SSH进程的stdin引用
        println!("发送命令到会话 {}: {}", session_id, command);
        Ok(())
    }

    pub fn close_session(&self, session_id: &str) {
        let mut sessions = self.sessions.lock().unwrap();
        sessions.remove(session_id);
        
        let mut senders = self.output_senders.lock().unwrap();
        senders.remove(session_id);
    }

    pub fn get_session(&self, session_id: &str) -> Option<SshTerminalSession> {
        let sessions = self.sessions.lock().unwrap();
        sessions.get(session_id).cloned()
    }
}

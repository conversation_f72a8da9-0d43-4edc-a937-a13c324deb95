use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::process::Command;
use std::io::Read;
use tokio::sync::mpsc;
use uuid::Uuid;
use crate::models::{Host, SshTerminalSession};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TerminalOutput {
    pub session_id: String,
    pub output: String,
    pub output_type: String, // "stdout", "stderr", "info"
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TerminalCommand {
    pub session_id: String,
    pub command: String,
}

pub struct SshTerminalService {
    sessions: Arc<Mutex<HashMap<String, SshTerminalSession>>>,
    output_senders: Arc<Mutex<HashMap<String, mpsc::UnboundedSender<TerminalOutput>>>>,
}

impl SshTerminalService {
    pub fn new() -> Self {
        Self {
            sessions: Arc::new(Mutex::new(HashMap::new())),
            output_senders: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn create_session(&self, host: Host) -> Result<String, String> {
        let session_id = Uuid::new_v4().to_string();
        
        // 创建会话记录
        let session = SshTerminalSession {
            session_id: session_id.clone(),
            host_id: host.id,
            created_at: chrono::Utc::now().to_rfc3339(),
        };

        // 存储会话
        {
            let mut sessions = self.sessions.lock().unwrap();
            sessions.insert(session_id.clone(), session);
        }

        // 创建输出通道
        let (tx, _rx) = mpsc::unbounded_channel::<TerminalOutput>();
        {
            let mut senders = self.output_senders.lock().unwrap();
            senders.insert(session_id.clone(), tx.clone());
        }

        // 启动SSH连接
        let session_id_clone = session_id.clone();
        let host_clone = host.clone();
        
        tokio::spawn(async move {
            if let Err(e) = Self::start_ssh_session(host_clone, session_id_clone.clone(), tx.clone()).await {
                let _ = tx.send(TerminalOutput {
                    session_id: session_id_clone,
                    output: format!("SSH连接失败: {}", e),
                    output_type: "stderr".to_string(),
                });
            }
        });

        Ok(session_id)
    }

    async fn start_ssh_session(
        host: Host,
        session_id: String,
        output_sender: mpsc::UnboundedSender<TerminalOutput>
    ) -> Result<(), String> {
        // 发送连接信息
        let _ = output_sender.send(TerminalOutput {
            session_id: session_id.clone(),
            output: format!("正在连接到 {}@{}:{}...\n", host.username, host.hostname, host.port),
            output_type: "info".to_string(),
        });

        // 创建真正的SSH连接
        match crate::ssh_executor::SshExecutor::create_ssh_session(&host).await {
            Ok(session) => {
                let _ = output_sender.send(TerminalOutput {
                    session_id: session_id.clone(),
                    output: format!("已连接到 {}@{}:{}\n", host.username, host.hostname, host.port),
                    output_type: "info".to_string(),
                });

                // 发送欢迎信息
                let _ = output_sender.send(TerminalOutput {
                    session_id: session_id.clone(),
                    output: "SSH连接建立成功，现在可以执行任何Linux命令\n".to_string(),
                    output_type: "info".to_string(),
                });

                let _ = output_sender.send(TerminalOutput {
                    session_id: session_id.clone(),
                    output: format!("{}@{}:~$ ", host.username, host.hostname),
                    output_type: "stdout".to_string(),
                });
            }
            Err(e) => {
                let _ = output_sender.send(TerminalOutput {
                    session_id: session_id.clone(),
                    output: format!("SSH连接失败: {}\n", e),
                    output_type: "stderr".to_string(),
                });
            }
        }

        Ok(())
    }

    pub async fn send_command(&self, session_id: &str, command: &str) -> Result<(), String> {
        // 获取会话信息
        let session_info = {
            let sessions = self.sessions.lock().unwrap();
            sessions.get(session_id).cloned()
        };

        if let Some(session) = session_info {
            // 执行真正的SSH命令
            self.execute_ssh_command(&session.host, command, session_id).await?;
        } else {
            return Err("Session not found".to_string());
        }

        Ok(())
    }

    async fn execute_ssh_command(&self, host: &Host, command: &str, session_id: &str) -> Result<(), String> {
        // 获取输出发送器
        let sender = {
            let senders = self.output_senders.lock().unwrap();
            senders.get(session_id).cloned()
        };

        if let Some(output_sender) = sender {
            // 创建SSH连接并执行命令
            match crate::ssh_executor::SshExecutor::create_ssh_session(host).await {
                Ok(session) => {
                    match session.channel_session() {
                        Ok(mut channel) => {
                            match channel.exec(command) {
                                Ok(_) => {
                                    // 读取命令输出
                                    let mut output = String::new();
                                    match channel.read_to_string(&mut output) {
                                        Ok(_) => {
                                            // 等待命令完成
                                            channel.wait_close().ok();
                                            let exit_status = channel.exit_status().unwrap_or(-1);

                                            // 发送命令输出
                                            if !output.is_empty() {
                                                let _ = output_sender.send(TerminalOutput {
                                                    session_id: session_id.to_string(),
                                                    output: output,
                                                    output_type: "stdout".to_string(),
                                                });
                                            }

                                            // 如果命令失败，读取错误输出
                                            if exit_status != 0 {
                                                let mut stderr_output = String::new();
                                                let _ = channel.stderr().read_to_string(&mut stderr_output);
                                                if !stderr_output.is_empty() {
                                                    let _ = output_sender.send(TerminalOutput {
                                                        session_id: session_id.to_string(),
                                                        output: stderr_output,
                                                        output_type: "stderr".to_string(),
                                                    });
                                                }
                                            }

                                            // 发送新的提示符
                                            let _ = output_sender.send(TerminalOutput {
                                                session_id: session_id.to_string(),
                                                output: format!("{}@{}:~$ ", host.username, host.hostname),
                                                output_type: "stdout".to_string(),
                                            });
                                        }
                                        Err(e) => {
                                            let _ = output_sender.send(TerminalOutput {
                                                session_id: session_id.to_string(),
                                                output: format!("读取命令输出失败: {}\n", e),
                                                output_type: "stderr".to_string(),
                                            });
                                        }
                                    }
                                }
                                Err(e) => {
                                    let _ = output_sender.send(TerminalOutput {
                                        session_id: session_id.to_string(),
                                        output: format!("执行命令失败: {}\n", e),
                                        output_type: "stderr".to_string(),
                                    });
                                }
                            }
                        }
                        Err(e) => {
                            let _ = output_sender.send(TerminalOutput {
                                session_id: session_id.to_string(),
                                output: format!("创建SSH通道失败: {}\n", e),
                                output_type: "stderr".to_string(),
                            });
                        }
                    }
                }
                Err(e) => {
                    let _ = output_sender.send(TerminalOutput {
                        session_id: session_id.to_string(),
                        output: format!("SSH连接失败: {}\n", e),
                        output_type: "stderr".to_string(),
                    });
                }
            }
        }

        Ok(())
    }



    pub fn close_session(&self, session_id: &str) {
        let mut sessions = self.sessions.lock().unwrap();
        sessions.remove(session_id);
        
        let mut senders = self.output_senders.lock().unwrap();
        senders.remove(session_id);
    }

    #[allow(dead_code)]
    pub fn get_session(&self, session_id: &str) -> Option<SshTerminalSession> {
        let sessions = self.sessions.lock().unwrap();
        sessions.get(session_id).cloned()
    }
}

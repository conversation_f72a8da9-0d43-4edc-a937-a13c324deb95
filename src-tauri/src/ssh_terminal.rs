use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::process::Command;
use tokio::sync::mpsc;
use uuid::Uuid;
use crate::models::{Host, SshTerminalSession};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TerminalOutput {
    pub session_id: String,
    pub output: String,
    pub output_type: String, // "stdout", "stderr", "info"
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TerminalCommand {
    pub session_id: String,
    pub command: String,
}

pub struct SshTerminalService {
    sessions: Arc<Mutex<HashMap<String, SshTerminalSession>>>,
    output_senders: Arc<Mutex<HashMap<String, mpsc::UnboundedSender<TerminalOutput>>>>,
}

impl SshTerminalService {
    pub fn new() -> Self {
        Self {
            sessions: Arc::new(Mutex::new(HashMap::new())),
            output_senders: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn create_session(&self, host: Host) -> Result<String, String> {
        let session_id = Uuid::new_v4().to_string();
        
        // 创建会话记录
        let session = SshTerminalSession {
            session_id: session_id.clone(),
            host_id: host.id,
            created_at: chrono::Utc::now().to_rfc3339(),
        };

        // 存储会话
        {
            let mut sessions = self.sessions.lock().unwrap();
            sessions.insert(session_id.clone(), session);
        }

        // 创建输出通道
        let (tx, _rx) = mpsc::unbounded_channel::<TerminalOutput>();
        {
            let mut senders = self.output_senders.lock().unwrap();
            senders.insert(session_id.clone(), tx.clone());
        }

        // 启动SSH连接
        let session_id_clone = session_id.clone();
        let host_clone = host.clone();
        
        tokio::spawn(async move {
            if let Err(e) = Self::start_ssh_session(host_clone, session_id_clone.clone(), tx.clone()).await {
                let _ = tx.send(TerminalOutput {
                    session_id: session_id_clone,
                    output: format!("SSH连接失败: {}", e),
                    output_type: "stderr".to_string(),
                });
            }
        });

        Ok(session_id)
    }

    async fn start_ssh_session(
        host: Host,
        session_id: String,
        output_sender: mpsc::UnboundedSender<TerminalOutput>
    ) -> Result<(), String> {
        // 发送连接信息
        let _ = output_sender.send(TerminalOutput {
            session_id: session_id.clone(),
            output: format!("正在连接到 {}@{}:{}...\n", host.username, host.hostname, host.port),
            output_type: "info".to_string(),
        });

        // 简化的SSH连接测试 - 使用ping代替实际SSH连接
        let ping_result = tokio::task::spawn_blocking({
            let hostname = host.hostname.clone();
            move || {
                #[cfg(target_os = "windows")]
                let output = Command::new("ping")
                    .args(["-n", "1", "-w", "3000", &hostname])
                    .output();

                #[cfg(not(target_os = "windows"))]
                let output = Command::new("ping")
                    .args(["-c", "1", "-W", "3", &hostname])
                    .output();

                output
            }
        }).await;

        match ping_result {
            Ok(Ok(result)) => {
                if result.status.success() {
                    let _ = output_sender.send(TerminalOutput {
                        session_id: session_id.clone(),
                        output: format!("已连接到 {}@{}\n", host.username, host.hostname),
                        output_type: "info".to_string(),
                    });

                    // 模拟终端提示符
                    let _ = output_sender.send(TerminalOutput {
                        session_id: session_id.clone(),
                        output: format!("{}@{}:~$ ", host.username, host.hostname),
                        output_type: "stdout".to_string(),
                    });

                    // 发送欢迎信息
                    let _ = output_sender.send(TerminalOutput {
                        session_id: session_id.clone(),
                        output: "欢迎使用SSH终端模拟器\n".to_string(),
                        output_type: "info".to_string(),
                    });

                    let _ = output_sender.send(TerminalOutput {
                        session_id: session_id.clone(),
                        output: "支持的命令: ls, pwd, whoami, date, echo\n".to_string(),
                        output_type: "info".to_string(),
                    });
                } else {
                    let _ = output_sender.send(TerminalOutput {
                        session_id: session_id.clone(),
                        output: format!("连接失败: 无法连接到主机 {}\n", host.hostname),
                        output_type: "stderr".to_string(),
                    });
                }
            }
            _ => {
                let _ = output_sender.send(TerminalOutput {
                    session_id: session_id.clone(),
                    output: "连接失败: 网络错误\n".to_string(),
                    output_type: "stderr".to_string(),
                });
            }
        }

        Ok(())
    }

    pub async fn send_command(&self, session_id: &str, command: &str) -> Result<(), String> {
        // 获取输出发送器
        let sender = {
            let senders = self.output_senders.lock().unwrap();
            senders.get(session_id).cloned()
        };

        if let Some(output_sender) = sender {
            // 模拟命令执行
            let response = self.execute_mock_command(command);

            // 发送命令回显
            let _ = output_sender.send(TerminalOutput {
                session_id: session_id.to_string(),
                output: response,
                output_type: "stdout".to_string(),
            });

            // 发送新的提示符
            let _ = output_sender.send(TerminalOutput {
                session_id: session_id.to_string(),
                output: "user@host:~$ ".to_string(),
                output_type: "stdout".to_string(),
            });
        }

        Ok(())
    }

    fn execute_mock_command(&self, command: &str) -> String {
        let cmd = command.trim();

        match cmd {
            "ls" => "Desktop  Documents  Downloads  Pictures  Videos\n".to_string(),
            "pwd" => "/home/<USER>".to_string(),
            "whoami" => "user\n".to_string(),
            "date" => {
                let now = chrono::Utc::now();
                format!("{}\n", now.format("%a %b %d %H:%M:%S UTC %Y"))
            },
            cmd if cmd.starts_with("echo ") => {
                let text = &cmd[5..]; // 移除 "echo "
                format!("{}\n", text)
            },
            "help" => {
                "支持的命令:\n  ls      - 列出文件\n  pwd     - 显示当前目录\n  whoami  - 显示用户名\n  date    - 显示日期时间\n  echo    - 输出文本\n  help    - 显示帮助\n".to_string()
            },
            "" => "".to_string(), // 空命令
            _ => format!("bash: {}: command not found\n", cmd),
        }
    }

    pub fn close_session(&self, session_id: &str) {
        let mut sessions = self.sessions.lock().unwrap();
        sessions.remove(session_id);
        
        let mut senders = self.output_senders.lock().unwrap();
        senders.remove(session_id);
    }

    #[allow(dead_code)]
    pub fn get_session(&self, session_id: &str) -> Option<SshTerminalSession> {
        let sessions = self.sessions.lock().unwrap();
        sessions.get(session_id).cloned()
    }
}

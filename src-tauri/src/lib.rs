mod models;
mod database;
mod ssh_executor;
mod commands;
mod logger;
mod ping_service;
mod ssh_terminal;

use std::sync::Arc;
use commands::AppState;
use ping_service::PingService;
use ssh_terminal::SshTerminalService;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tokio::runtime::Runtime::new().unwrap().block_on(async {
        // Initialize database - use parent directory (project root)
        let current_dir = std::env::current_dir().expect("Failed to get current directory");
        let project_root = current_dir.parent().unwrap_or(&current_dir);

        // Ensure the project root directory exists and is writable
        if let Err(e) = std::fs::create_dir_all(&project_root) {
            eprintln!("Failed to create project root directory: {}", e);
        }

        let db_path = project_root.join("db.db");

        // Try to create the database file if it doesn't exist
        if !db_path.exists() {
            if let Err(e) = std::fs::File::create(&db_path) {
                eprintln!("Failed to create database file: {}", e);
            }
        }
        let db_path_str = db_path.to_string_lossy();
        println!("Using database path: {}", db_path_str);

        let db = Arc::new(database::Database::new(&db_path_str).await.expect("Failed to initialize database"));

        // Initialize logger after database is ready
        if let Err(e) = logger::AppLogger::init() {
            eprintln!("Failed to initialize logger: {}", e);
        } else {
            logger::AppLogger::log_operation("APP_START", &format!("Database path: {}", db_path_str));
            logger::AppLogger::log_operation("DB_INIT", "Database initialized successfully");
        }
        
        // Initialize SSH executor
        let ssh_executor = Arc::new(ssh_executor::SshExecutor::new(db.clone()));

        // Initialize ping service
        let ping_service = Arc::new(PingService::new());

        // Initialize SSH terminal service
        let ssh_terminal_service = Arc::new(SshTerminalService::new());

        // Create app state
        let app_state = AppState {
            db: db.clone(),
            ssh_executor,
            ping_service: ping_service.clone(),
            ssh_terminal_service,
        };

        // 启动主机监控
        let ping_service_clone = ping_service.clone();
        let db_clone = db.clone();
        tokio::spawn(async move {
            if let Ok(hosts) = db_clone.get_all_hosts().await {
                ping_service_clone.start_monitoring(hosts).await;
                println!("Host monitoring started");
            }
        });

        tauri::Builder::default()
            .plugin(tauri_plugin_opener::init())
            .manage(app_state)
            .invoke_handler(tauri::generate_handler![
                commands::get_hosts,
                commands::get_host,
                commands::create_host,
                commands::create_hosts_batch,
                commands::update_host,
                commands::delete_host,
                commands::delete_hosts_batch,
                commands::test_host_connection,
                commands::get_tasks,
                commands::get_task,
                commands::create_task,
                commands::delete_task,
                commands::delete_tasks_batch,
                commands::execute_task,
                commands::cancel_task,
                commands::is_task_running,
                commands::get_task_logs,
                commands::clear_task_logs,
                commands::subscribe_task_logs,
                commands::get_app_data_dir,
                commands::get_host_status,
                commands::get_all_host_statuses,
                commands::start_host_monitoring,
                commands::create_ssh_session,
                commands::send_ssh_command,
                commands::close_ssh_session,
            ])
            .setup(|_app| {
                Ok(())
            })
            .run(tauri::generate_context!())
            .expect("error while running tauri application");
    });
}

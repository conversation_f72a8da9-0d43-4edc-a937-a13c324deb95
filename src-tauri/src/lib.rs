mod models;
mod database;
mod ssh_executor;
mod commands;

use std::sync::Arc;
use commands::AppState;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tokio::runtime::Runtime::new().unwrap().block_on(async {
        // Initialize database
        let home_dir = std::env::var("HOME")
            .or_else(|_| std::env::var("USERPROFILE"))
            .unwrap_or_else(|_| ".".to_string());
        
        let app_data_dir = format!("{}/.local/share/sshmanger", home_dir);
        
        // Create the directory if it doesn't exist
        if let Err(e) = std::fs::create_dir_all(&app_data_dir) {
            eprintln!("Warning: Failed to create app data directory: {}", e);
        }
        
        let db_path = format!("{}/db.db", app_data_dir);
        let db = Arc::new(database::Database::new(&db_path).await.expect("Failed to initialize database"));
        
        // Initialize SSH executor
        let ssh_executor = Arc::new(ssh_executor::SshExecutor::new(db.clone()));
        
        // Create app state
        let app_state = AppState {
            db,
            ssh_executor,
        };

        tauri::Builder::default()
            .plugin(tauri_plugin_opener::init())
            .manage(app_state)
            .invoke_handler(tauri::generate_handler![
                commands::get_hosts,
                commands::get_host,
                commands::create_host,
                commands::update_host,
                commands::delete_host,
                commands::test_host_connection,
                commands::get_tasks,
                commands::get_task,
                commands::create_task,
                commands::delete_task,
                commands::execute_task,
                commands::cancel_task,
                commands::is_task_running,
                commands::get_task_logs,
                commands::clear_task_logs,
                commands::subscribe_task_logs,
                commands::get_app_data_dir,
            ])
            .setup(|_app| {
                Ok(())
            })
            .run(tauri::generate_context!())
            .expect("error while running tauri application");
    });
}

mod models;
mod database;
mod ssh_executor;
mod commands;

use std::sync::Arc;
use commands::AppState;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tokio::runtime::Runtime::new().unwrap().block_on(async {
        // Initialize database - use absolute path
        let current_dir = std::env::current_dir().expect("Failed to get current directory");
        let data_dir = current_dir.join("data");

        // Create the directory if it doesn't exist
        if let Err(e) = std::fs::create_dir_all(&data_dir) {
            eprintln!("Failed to create data directory: {}", e);
        }

        let db_path = data_dir.join("db.db");
        let db_path_str = db_path.to_string_lossy();
        println!("Using database path: {}", db_path_str);

        let db = Arc::new(database::Database::new(&db_path_str).await.expect("Failed to initialize database"));
        
        // Initialize SSH executor
        let ssh_executor = Arc::new(ssh_executor::SshExecutor::new(db.clone()));
        
        // Create app state
        let app_state = AppState {
            db,
            ssh_executor,
        };

        tauri::Builder::default()
            .plugin(tauri_plugin_opener::init())
            .manage(app_state)
            .invoke_handler(tauri::generate_handler![
                commands::get_hosts,
                commands::get_host,
                commands::create_host,
                commands::create_hosts_batch,
                commands::update_host,
                commands::delete_host,
                commands::delete_hosts_batch,
                commands::test_host_connection,
                commands::get_tasks,
                commands::get_task,
                commands::create_task,
                commands::delete_task,
                commands::delete_tasks_batch,
                commands::execute_task,
                commands::cancel_task,
                commands::is_task_running,
                commands::get_task_logs,
                commands::clear_task_logs,
                commands::subscribe_task_logs,
                commands::get_app_data_dir,
            ])
            .setup(|_app| {
                Ok(())
            })
            .run(tauri::generate_context!())
            .expect("error while running tauri application");
    });
}

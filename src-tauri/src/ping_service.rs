use std::process::Command;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::time::interval;
use crate::models::{Host, HostStatus};
use chrono::Utc;

pub struct PingService {
    host_statuses: Arc<Mutex<HashMap<i64, HostStatus>>>,
}

impl PingService {
    pub fn new() -> Self {
        Self {
            host_statuses: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn start_monitoring(&self, hosts: Vec<Host>) {
        let statuses = self.host_statuses.clone();
        
        // 初始化所有主机状态
        {
            let mut status_map = statuses.lock().unwrap();
            for host in &hosts {
                status_map.insert(host.id, HostStatus {
                    host_id: host.id,
                    status: "checking".to_string(),
                    ping_time: None,
                    last_check: Utc::now().to_rfc3339(),
                });
            }
        }

        // 启动定时检测任务
        let hosts_clone = hosts.clone();
        let statuses_clone = statuses.clone();
        
        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                // 并行检测所有主机
                let mut tasks = Vec::new();
                
                for host in &hosts_clone {
                    let host_clone = host.clone();
                    let statuses_ref = statuses_clone.clone();
                    
                    let task = tokio::spawn(async move {
                        let ping_result = Self::ping_host(&host_clone.hostname).await;
                        
                        let mut status_map = statuses_ref.lock().unwrap();
                        if let Some(status) = status_map.get_mut(&host_clone.id) {
                            match ping_result {
                                Ok(ping_time) => {
                                    status.status = "online".to_string();
                                    status.ping_time = Some(ping_time);
                                }
                                Err(_) => {
                                    status.status = "offline".to_string();
                                    status.ping_time = None;
                                }
                            }
                            status.last_check = Utc::now().to_rfc3339();
                        }
                    });
                    
                    tasks.push(task);
                }
                
                // 等待所有ping任务完成
                for task in tasks {
                    let _ = task.await;
                }
            }
        });
    }

    async fn ping_host(hostname: &str) -> Result<f64, String> {
        let start = Instant::now();
        
        // 使用系统ping命令
        let output = tokio::task::spawn_blocking({
            let hostname = hostname.to_string();
            move || {
                #[cfg(target_os = "windows")]
                let output = Command::new("ping")
                    .args(["-n", "1", "-w", "3000", &hostname])
                    .output();
                
                #[cfg(not(target_os = "windows"))]
                let output = Command::new("ping")
                    .args(["-c", "1", "-W", "3", &hostname])
                    .output();
                
                output
            }
        }).await;

        match output {
            Ok(Ok(result)) => {
                if result.status.success() {
                    let duration = start.elapsed();
                    Ok(duration.as_millis() as f64)
                } else {
                    Err("Ping failed".to_string())
                }
            }
            _ => Err("Ping command failed".to_string()),
        }
    }

    pub fn get_host_status(&self, host_id: i64) -> Option<HostStatus> {
        let status_map = self.host_statuses.lock().unwrap();
        status_map.get(&host_id).cloned()
    }

    pub fn get_all_statuses(&self) -> HashMap<i64, HostStatus> {
        let status_map = self.host_statuses.lock().unwrap();
        status_map.clone()
    }

    #[allow(dead_code)]
    pub fn update_hosts(&self, hosts: Vec<Host>) {
        let mut status_map = self.host_statuses.lock().unwrap();
        
        // 移除不存在的主机
        let host_ids: std::collections::HashSet<i64> = hosts.iter().map(|h| h.id).collect();
        status_map.retain(|&k, _| host_ids.contains(&k));
        
        // 添加新主机
        for host in hosts {
            if !status_map.contains_key(&host.id) {
                status_map.insert(host.id, HostStatus {
                    host_id: host.id,
                    status: "checking".to_string(),
                    ping_time: None,
                    last_check: Utc::now().to_rfc3339(),
                });
            }
        }
    }
}

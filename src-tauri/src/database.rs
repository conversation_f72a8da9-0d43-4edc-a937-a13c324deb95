use sqlx::{Pool, Sqlite, SqlitePool};
use chrono::Utc;
use crate::models::{Host, Task, TaskLog, CreateHostRequest, UpdateHostRequest, CreateTaskRequest, LogEntry};
use anyhow::Result;
use std::path::Path;

pub struct Database {
    pool: Pool<Sqlite>,
}

impl Database {
    pub async fn new(database_path: &str) -> Result<Self> {
        println!("正在初始化数据库: {}", database_path);
        
        // Create database directory if it doesn't exist
        if let Some(parent) = Path::new(database_path).parent() {
            println!("创建数据库目录: {:?}", parent);
            tokio::fs::create_dir_all(parent).await?;
        }

        // Check if database file exists
        let db_exists = Path::new(database_path).exists();
        if !db_exists {
            println!("数据库文件不存在，将创建新的数据库: {}", database_path);
        } else {
            println!("数据库文件已存在: {}", database_path);
        }

        let database_url = format!("sqlite:{}", database_path);
        println!("连接到数据库: {}", database_url);
        let pool = SqlitePool::connect(&database_url).await?;
        
        let db = Database { pool };
        println!("运行数据库迁移，创建必要的表...");
        db.run_migrations().await?;
        println!("数据库初始化完成!");
        
        Ok(db)
    }

    async fn run_migrations(&self) -> Result<()> {
        // Create hosts table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS hosts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                hostname TEXT NOT NULL,
                port INTEGER NOT NULL DEFAULT 22,
                username TEXT NOT NULL,
                password TEXT,
                private_key TEXT,
                description TEXT,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create tasks table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                task_type TEXT NOT NULL,
                command TEXT,
                script_content TEXT,
                source_path TEXT,
                target_path TEXT,
                host_ids TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                parallel BOOLEAN NOT NULL DEFAULT false,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                started_at DATETIME,
                completed_at DATETIME
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        // Create task_logs table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS task_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER NOT NULL,
                host_id INTEGER NOT NULL,
                log_type TEXT NOT NULL,
                content TEXT NOT NULL,
                timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES tasks (id),
                FOREIGN KEY (host_id) REFERENCES hosts (id)
            )
            "#,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // Host management methods
    pub async fn create_host(&self, request: CreateHostRequest) -> Result<Host> {
        let now = Utc::now();
        let result = sqlx::query_as::<_, Host>(
            r#"
            INSERT INTO hosts (name, hostname, port, username, password, private_key, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
        )
        .bind(&request.name)
        .bind(&request.hostname)
        .bind(request.port)
        .bind(&request.username)
        .bind(&request.password)
        .bind(&request.private_key)
        .bind(&request.description)
        .bind(now)
        .bind(now)
        .fetch_one(&self.pool)
        .await?;

        Ok(result)
    }

    pub async fn get_all_hosts(&self) -> Result<Vec<Host>> {
        let hosts = sqlx::query_as::<_, Host>("SELECT * FROM hosts ORDER BY created_at DESC")
            .fetch_all(&self.pool)
            .await?;
        Ok(hosts)
    }

    pub async fn get_host_by_id(&self, id: i64) -> Result<Option<Host>> {
        let host = sqlx::query_as::<_, Host>("SELECT * FROM hosts WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await?;
        Ok(host)
    }

    pub async fn update_host(&self, request: UpdateHostRequest) -> Result<Host> {
        let now = Utc::now();
        let result = sqlx::query_as::<_, Host>(
            r#"
            UPDATE hosts 
            SET name = ?, hostname = ?, port = ?, username = ?, password = ?, private_key = ?, description = ?, updated_at = ?
            WHERE id = ?
            RETURNING *
            "#,
        )
        .bind(&request.name)
        .bind(&request.hostname)
        .bind(request.port)
        .bind(&request.username)
        .bind(&request.password)
        .bind(&request.private_key)
        .bind(&request.description)
        .bind(now)
        .bind(request.id)
        .fetch_one(&self.pool)
        .await?;

        Ok(result)
    }

    pub async fn delete_host(&self, id: i64) -> Result<()> {
        sqlx::query("DELETE FROM hosts WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    // Task management methods
    pub async fn create_task(&self, request: CreateTaskRequest) -> Result<Task> {
        let now = Utc::now();
        let host_ids_json = serde_json::to_string(&request.host_ids)?;
        
        let result = sqlx::query_as::<_, Task>(
            r#"
            INSERT INTO tasks (name, task_type, command, script_content, source_path, target_path, host_ids, parallel, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
        )
        .bind(&request.name)
        .bind(&request.task_type)
        .bind(&request.command)
        .bind(&request.script_content)
        .bind(&request.source_path)
        .bind(&request.target_path)
        .bind(&host_ids_json)
        .bind(request.parallel)
        .bind(now)
        .bind(now)
        .fetch_one(&self.pool)
        .await?;

        Ok(result)
    }

    pub async fn get_all_tasks(&self) -> Result<Vec<Task>> {
        let tasks = sqlx::query_as::<_, Task>("SELECT * FROM tasks ORDER BY created_at DESC")
            .fetch_all(&self.pool)
            .await?;
        Ok(tasks)
    }

    pub async fn get_task_by_id(&self, id: i64) -> Result<Option<Task>> {
        let task = sqlx::query_as::<_, Task>("SELECT * FROM tasks WHERE id = ?")
            .bind(id)
            .fetch_optional(&self.pool)
            .await?;
        Ok(task)
    }

    pub async fn update_task_status(&self, id: i64, status: &str) -> Result<()> {
        let now = Utc::now();
        
        if status == "running" {
            sqlx::query("UPDATE tasks SET status = ?, updated_at = ?, started_at = ? WHERE id = ?")
                .bind(status)
                .bind(now)
                .bind(now)
                .bind(id)
                .execute(&self.pool)
                .await?;
        } else if status == "completed" || status == "failed" {
            sqlx::query("UPDATE tasks SET status = ?, updated_at = ?, completed_at = ? WHERE id = ?")
                .bind(status)
                .bind(now)
                .bind(now)
                .bind(id)
                .execute(&self.pool)
                .await?;
        } else {
            sqlx::query("UPDATE tasks SET status = ?, updated_at = ? WHERE id = ?")
                .bind(status)
                .bind(now)
                .bind(id)
                .execute(&self.pool)
                .await?;
        }

        Ok(())
    }

    pub async fn delete_task(&self, id: i64) -> Result<()> {
        // Delete associated logs first
        sqlx::query("DELETE FROM task_logs WHERE task_id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;
        
        // Delete the task
        sqlx::query("DELETE FROM tasks WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;
        
        Ok(())
    }

    // Log management methods
    pub async fn add_task_log(&self, task_id: i64, host_id: i64, log_type: &str, content: &str) -> Result<()> {
        let now = Utc::now();
        sqlx::query(
            "INSERT INTO task_logs (task_id, host_id, log_type, content, timestamp) VALUES (?, ?, ?, ?, ?)"
        )
        .bind(task_id)
        .bind(host_id)
        .bind(log_type)
        .bind(content)
        .bind(now)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }

    pub async fn get_task_logs(&self, task_id: i64) -> Result<Vec<LogEntry>> {
        let logs = sqlx::query_as::<_, (i64, i64, i64, String, String, String, chrono::DateTime<Utc>)>(
            r#"
            SELECT tl.id, tl.task_id, tl.host_id, h.name as host_name, tl.log_type, tl.content, tl.timestamp
            FROM task_logs tl
            JOIN hosts h ON tl.host_id = h.id
            WHERE tl.task_id = ?
            ORDER BY tl.timestamp ASC
            "#
        )
        .bind(task_id)
        .fetch_all(&self.pool)
        .await?;

        let log_entries: Vec<LogEntry> = logs
            .into_iter()
            .map(|(id, task_id, host_id, host_name, log_type, content, timestamp)| LogEntry {
                id,
                task_id,
                host_id,
                host_name,
                log_type,
                content,
                timestamp,
            })
            .collect();

        Ok(log_entries)
    }

    pub async fn get_task_logs_by_host(&self, task_id: i64, host_id: i64) -> Result<Vec<TaskLog>> {
        let logs = sqlx::query_as::<_, TaskLog>(
            "SELECT * FROM task_logs WHERE task_id = ? AND host_id = ? ORDER BY timestamp ASC"
        )
        .bind(task_id)
        .bind(host_id)
        .fetch_all(&self.pool)
        .await?;
        
        Ok(logs)
    }

    pub async fn clear_task_logs(&self, task_id: i64) -> Result<()> {
        sqlx::query("DELETE FROM task_logs WHERE task_id = ?")
            .bind(task_id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }
}

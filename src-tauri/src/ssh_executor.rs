use std::io::prelude::*;
use std::path::Path;
use std::sync::Arc;
use std::time::Duration;

use anyhow::{anyhow, Result};
use dashmap::DashMap;
use ssh2::Session;
use tokio::sync::broadcast;
use tokio::time::timeout;
use futures;

use crate::database::Database;
use crate::models::{Host, Task};

pub struct SshExecutor {
    db: Arc<Database>,
    running_tasks: Arc<DashMap<i64, broadcast::Sender<String>>>,
}

impl SshExecutor {
    pub fn new(db: Arc<Database>) -> Self {
        Self {
            db,
            running_tasks: Arc::new(DashMap::new()),
        }
    }

    pub async fn execute_task(&self, task_id: i64) -> Result<()> {
        let task = self.db.get_task_by_id(task_id).await?
            .ok_or_else(|| anyhow!("Task not found"))?;

        let host_ids: Vec<i64> = serde_json::from_str(&task.host_ids)?;
        let hosts = self.get_hosts_by_ids(&host_ids).await?;

        // Create a broadcast channel for real-time log streaming
        let (tx, _) = broadcast::channel::<String>(1000);
        self.running_tasks.insert(task_id, tx.clone());

        // Update task status to running
        self.db.update_task_status(task_id, "running").await?;

        let result = if task.parallel {
            self.execute_parallel(&task, &hosts, tx.clone()).await
        } else {
            self.execute_sequential(&task, &hosts, tx.clone()).await
        };

        // Update final status
        match &result {
            Ok(_) => {
                self.db.update_task_status(task_id, "completed").await?;
                let _ = tx.send("Task completed successfully".to_string());
            }
            Err(e) => {
                self.db.update_task_status(task_id, "failed").await?;
                let _ = tx.send(format!("Task failed: {}", e));
            }
        }

        // Clean up the broadcast channel
        self.running_tasks.remove(&task_id);

        result
    }

    async fn execute_parallel(
        &self,
        task: &Task,
        hosts: &[Host],
        tx: broadcast::Sender<String>,
    ) -> Result<()> {
        let futures = hosts.iter().map(|host| {
            let task_clone = task.clone();
            let host_clone = host.clone();
            let tx_clone = tx.clone();
            let db_clone = self.db.clone();
            
            tokio::spawn(async move {
                Self::execute_on_host(db_clone, &task_clone, &host_clone, tx_clone).await
            })
        });

        let results = futures::future::join_all(futures).await;
        
        // Check if any task failed
        for result in results {
            if let Err(e) = result {
                return Err(anyhow!("Task execution error: {}", e));
            }
        }

        Ok(())
    }

    async fn execute_sequential(
        &self,
        task: &Task,
        hosts: &[Host],
        tx: broadcast::Sender<String>,
    ) -> Result<()> {
        for host in hosts {
            Self::execute_on_host(self.db.clone(), task, host, tx.clone()).await?;
        }
        Ok(())
    }

    async fn execute_on_host(
        db: Arc<Database>,
        task: &Task,
        host: &Host,
        tx: broadcast::Sender<String>,
    ) -> Result<()> {
        let log_prefix = format!("[{}] ", host.name);
        let _ = tx.send(format!("{}Starting execution...", log_prefix));

        // Add initial log
        db.add_task_log(task.id, host.id, "info", "Starting task execution").await?;

        match task.task_type.as_str() {
            "command" => {
                if let Some(command) = &task.command {
                    Self::execute_command(db, task, host, command, tx).await?;
                }
            }
            "script" => {
                if let Some(script) = &task.script_content {
                    Self::execute_script(db, task, host, script, tx).await?;
                }
            }
            "file_transfer" => {
                if let (Some(source), Some(target)) = (&task.source_path, &task.target_path) {
                    Self::transfer_file(db, task, host, source, target, tx).await?;
                }
            }
            _ => {
                return Err(anyhow!("Unknown task type: {}", task.task_type));
            }
        }

        Ok(())
    }

    /// Creates and configures an SSH session
    pub async fn create_ssh_session(host: &Host) -> Result<Session> {
        let tcp = timeout(
            Duration::from_secs(30),
            tokio::net::TcpStream::connect(format!("{}:{}", host.hostname, host.port)),
        ).await??;
        
        let tcp = tcp.into_std()?;
        let mut session = Session::new()?;
        session.set_tcp_stream(tcp);
        session.handshake()?;

        // Authenticate
        if let Some(private_key) = &host.private_key {
            if !private_key.trim().is_empty() {
                session.userauth_pubkey_memory(&host.username, None, private_key, None)?;
            } else if let Some(password) = &host.password {
                session.userauth_password(&host.username, password)?;
            } else {
                return Err(anyhow!("No authentication method provided"));
            }
        } else if let Some(password) = &host.password {
            session.userauth_password(&host.username, password)?;
        } else {
            return Err(anyhow!("No authentication method available"));
        }

        if !session.authenticated() {
            return Err(anyhow!("Authentication failed"));
        }

        Ok(session)
    }

    async fn execute_command(
        db: Arc<Database>,
        task: &Task,
        host: &Host,
        command: &str,
        tx: broadcast::Sender<String>,
    ) -> Result<()> {
        let log_prefix = format!("[{}] ", host.name);
        let _ = tx.send(format!("{}Executing command: {}", log_prefix, command));

        // Create SSH session
        let session = Self::create_ssh_session(host).await?;
        let mut channel = session.channel_session()
            .map_err(|e| anyhow!("Failed to create channel session: {}", e))?;

        // Execute command
        channel.exec(command)
            .map_err(|e| anyhow!("Failed to execute command: {}", e))?;

        // Read output with timeout
        let mut output = String::new();
        let mut stderr = String::new();

        // Read output
        channel.read_to_string(&mut output)?;

        // Read stderr
        channel.stderr().read_to_string(&mut stderr)?;

        // Wait for command to complete
        channel.wait_close()
            .map_err(|e| anyhow!("Failed to wait for command completion: {}", e))?;
        let exit_status = channel.exit_status()
            .map_err(|e| anyhow!("Failed to get exit status: {}", e))?;

        // Log outputs with proper error handling
        if !output.is_empty() {
            for line in output.lines() {
                let log_line = format!("{}{}", log_prefix, line);
                let _ = tx.send(log_line.clone());
                if let Err(e) = db.add_task_log(task.id, host.id, "stdout", line).await {
                    eprintln!("Failed to save log: {}", e);
                }
            }
        }

        if !stderr.is_empty() {
            for line in stderr.lines() {
                let log_line = format!("{}ERROR: {}", log_prefix, line);
                let _ = tx.send(log_line.clone());
                if let Err(e) = db.add_task_log(task.id, host.id, "stderr", line).await {
                    eprintln!("Failed to save log: {}", e);
                }
            }
        }

        if exit_status != 0 {
            let error_msg = format!("Command failed with exit code {}", exit_status);
            let _ = tx.send(format!("{}{}", log_prefix, error_msg));
            db.add_task_log(task.id, host.id, "error", &error_msg).await?;
            return Err(anyhow!(error_msg));
        }

        let _ = tx.send(format!("{}Command completed successfully", log_prefix));
        db.add_task_log(task.id, host.id, "info", "Command completed successfully").await?;

        Ok(())
    }

    async fn execute_script(
        db: Arc<Database>,
        task: &Task,
        host: &Host,
        script: &str,
        tx: broadcast::Sender<String>,
    ) -> Result<()> {
        let log_prefix = format!("[{}] ", host.name);
        let _ = tx.send(format!("{}Executing script...", log_prefix));

        // Create SSH session
        let session = Self::create_ssh_session(host).await?;
        
        // Create a temporary script file on the remote host
        let script_path = format!("/tmp/script_{}.sh", task.id);
        let mut scp = session.scp_send(Path::new(&script_path), 0o755, script.len() as u64, None)?;
        scp.write_all(script.as_bytes())?;
        scp.send_eof()?;
        scp.wait_eof()?;
        scp.close()?;
        scp.wait_close()?;

        // Execute the script
        let mut channel = session.channel_session()?;
        channel.exec(&format!("bash {}", script_path))?;

        // Read output
        let mut output = String::new();
        channel.read_to_string(&mut output)?;

        // Read stderr
        let mut stderr = String::new();
        channel.stderr().read_to_string(&mut stderr)?;

        // Wait for script to complete
        channel.wait_close()?;
        let exit_status = channel.exit_status()?;

        // Clean up the script file
        let mut cleanup_channel = session.channel_session()?;
        cleanup_channel.exec(&format!("rm -f {}", script_path))?;
        cleanup_channel.wait_close()?;

        // Log outputs
        if !output.is_empty() {
            for line in output.lines() {
                let log_line = format!("{}{}", log_prefix, line);
                let _ = tx.send(log_line.clone());
                db.add_task_log(task.id, host.id, "stdout", line).await?;
            }
        }

        if !stderr.is_empty() {
            for line in stderr.lines() {
                let log_line = format!("{}ERROR: {}", log_prefix, line);
                let _ = tx.send(log_line.clone());
                db.add_task_log(task.id, host.id, "stderr", line).await?;
            }
        }

        if exit_status != 0 {
            let error_msg = format!("Script failed with exit code {}", exit_status);
            let _ = tx.send(format!("{}{}", log_prefix, error_msg));
            db.add_task_log(task.id, host.id, "error", &error_msg).await?;
            return Err(anyhow!(error_msg));
        }

        let _ = tx.send(format!("{}Script completed successfully", log_prefix));
        db.add_task_log(task.id, host.id, "info", "Script completed successfully").await?;

        Ok(())
    }

    async fn transfer_file(
        db: Arc<Database>,
        task: &Task,
        host: &Host,
        source_path: &str,
        target_path: &str,
        tx: broadcast::Sender<String>,
    ) -> Result<()> {
        let log_prefix = format!("[{}] ", host.name);
        let _ = tx.send(format!("{}Transferring file: {} -> {}", log_prefix, source_path, target_path));

        // Create SSH session
        let session = Self::create_ssh_session(host).await?;

        // Read source file
        let source_data = tokio::fs::read(source_path).await?;
        
        // Upload file
        let mut scp = session.scp_send(
            Path::new(target_path),
            0o644,
            source_data.len() as u64,
            None,
        )?;
        scp.write_all(&source_data)?;
        scp.send_eof()?;
        scp.wait_eof()?;
        scp.close()?;
        scp.wait_close()?;

        let success_msg = format!("File transferred successfully: {} bytes", source_data.len());
        let _ = tx.send(format!("{}{}", log_prefix, success_msg));
        db.add_task_log(task.id, host.id, "info", &success_msg).await?;

        Ok(())
    }


    async fn get_hosts_by_ids(&self, host_ids: &[i64]) -> Result<Vec<Host>> {
        let mut hosts = Vec::new();
        for &host_id in host_ids {
            if let Some(host) = self.db.get_host_by_id(host_id).await? {
                hosts.push(host);
            }
        }
        Ok(hosts)
    }

    pub fn get_task_log_stream(&self, task_id: i64) -> Option<broadcast::Receiver<String>> {
        self.running_tasks.get(&task_id).map(|tx| tx.subscribe())
    }

    pub fn is_task_running(&self, task_id: i64) -> bool {
        self.running_tasks.contains_key(&task_id)
    }

    pub async fn cancel_task(&self, task_id: i64) -> Result<()> {
        if let Some((_, tx)) = self.running_tasks.remove(&task_id) {
            let _ = tx.send("Task cancelled by user".to_string());
            self.db.update_task_status(task_id, "cancelled").await?;
        }
        Ok(())
    }
}

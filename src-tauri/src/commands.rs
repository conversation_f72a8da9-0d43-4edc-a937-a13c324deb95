use std::sync::Arc;
use std::io::Read;
use tauri::{State, Emitter};
use crate::database::Database;
use crate::ssh_executor::SshExecutor;
use crate::models::{Host, Task, CreateHostRequest, UpdateHostRequest, CreateTaskRequest, LogEntry, HostStatus};
use crate::ping_service::PingService;
use crate::ssh_terminal::SshTerminalService;

// Application state
pub struct AppState {
    pub db: Arc<Database>,
    pub ssh_executor: Arc<SshExecutor>,
    pub ping_service: Arc<PingService>,
    pub ssh_terminal_service: Arc<SshTerminalService>,
}

// Host management commands
#[tauri::command]
pub async fn get_hosts(state: State<'_, AppState>) -> Result<Vec<Host>, String> {
    state.db.get_all_hosts().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_host(state: State<'_, AppState>, id: i64) -> Result<Option<Host>, String> {
    state.db.get_host_by_id(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_host(state: State<'_, AppState>, request: CreateHostRequest) -> Result<Host, String> {
    state.db.create_host(request).await.map_err(|e| {
        eprintln!("Error creating host: {}", e);
        e.to_string()
    })
}

#[tauri::command]
pub async fn create_hosts_batch(state: State<'_, AppState>, requests: Vec<CreateHostRequest>) -> Result<Vec<Host>, String> {
    println!("CREATE_HOSTS_BATCH: Attempting to create {} hosts", requests.len());

    match state.db.create_hosts_batch(requests.clone()).await {
        Ok(hosts) => {
            println!("CREATE_HOSTS_BATCH: Created {} out of {} hosts", hosts.len(), requests.len());
            Ok(hosts)
        }
        Err(e) => {
            eprintln!("CREATE_HOSTS_BATCH Error: {}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn update_host(state: State<'_, AppState>, request: UpdateHostRequest) -> Result<Host, String> {
    state.db.update_host(request).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_host(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    state.db.delete_host(id).await.map_err(|e| {
        eprintln!("Error deleting host {}: {}", id, e);
        e.to_string()
    })
}

#[tauri::command]
pub async fn delete_hosts_batch(state: State<'_, AppState>, ids: Vec<i64>) -> Result<Vec<i64>, String> {
    println!("DELETE_HOSTS_BATCH: Attempting to delete {} hosts: {:?}", ids.len(), ids);

    match state.db.delete_hosts_batch(ids.clone()).await {
        Ok(deleted_ids) => {
            println!("DELETE_HOSTS_BATCH: Deleted {} out of {} hosts: {:?}", deleted_ids.len(), ids.len(), deleted_ids);
            Ok(deleted_ids)
        }
        Err(e) => {
            eprintln!("DELETE_HOSTS_BATCH Error: {}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn test_host_connection(state: State<'_, AppState>, id: i64) -> Result<bool, String> {
    let host = state.db.get_host_by_id(id).await.map_err(|e| e.to_string())?;
    if let Some(host) = host {
        // Create a simple test task
        let test_command = "echo 'Connection test successful'";
        match crate::ssh_executor::SshExecutor::create_ssh_session(&host).await {
            Ok(session) => {
                match session.channel_session() {
                    Ok(mut channel) => {
                        match channel.exec(test_command) {
                            Ok(_) => {
                                let _ = channel.wait_close();
                                Ok(true)
                            },
                            Err(_) => Ok(false)
                        }
                    },
                    Err(_) => Ok(false)
                }
            },
            Err(_) => Ok(false)
        }
    } else {
        Err("Host not found".to_string())
    }
}

// Task management commands
#[tauri::command]
pub async fn get_tasks(state: State<'_, AppState>) -> Result<Vec<Task>, String> {
    state.db.get_all_tasks().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_task(state: State<'_, AppState>, id: i64) -> Result<Option<Task>, String> {
    state.db.get_task_by_id(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_task(state: State<'_, AppState>, request: CreateTaskRequest) -> Result<Task, String> {
    state.db.create_task(request).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    state.db.delete_task(id).await.map_err(|e| {
        eprintln!("Error deleting task {}: {}", id, e);
        e.to_string()
    })
}

#[tauri::command]
pub async fn delete_tasks_batch(state: State<'_, AppState>, ids: Vec<i64>) -> Result<Vec<i64>, String> {
    println!("DELETE_TASKS_BATCH: Attempting to delete {} tasks: {:?}", ids.len(), ids);

    match state.db.delete_tasks_batch(ids.clone()).await {
        Ok(deleted_ids) => {
            println!("DELETE_TASKS_BATCH: Deleted {} out of {} tasks: {:?}", deleted_ids.len(), ids.len(), deleted_ids);
            Ok(deleted_ids)
        }
        Err(e) => {
            eprintln!("DELETE_TASKS_BATCH Error: {}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn execute_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    let executor = state.ssh_executor.clone();
    tokio::spawn(async move {
        if let Err(e) = executor.execute_task(id).await {
            eprintln!("Task execution failed: {}", e);
        }
    });
    Ok(())
}

#[tauri::command]
pub async fn cancel_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    state.ssh_executor.cancel_task(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn is_task_running(state: State<'_, AppState>, id: i64) -> Result<bool, String> {
    Ok(state.ssh_executor.is_task_running(id))
}

// Log management commands
#[tauri::command]
pub async fn get_task_logs(state: State<'_, AppState>, task_id: i64) -> Result<Vec<LogEntry>, String> {
    state.db.get_task_logs(task_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn clear_task_logs(state: State<'_, AppState>, task_id: i64) -> Result<(), String> {
    state.db.clear_task_logs(task_id).await.map_err(|e| e.to_string())
}

// Real-time log streaming
#[tauri::command]
pub async fn subscribe_task_logs(
    state: State<'_, AppState>,
    task_id: i64,
    window: tauri::Window,
) -> Result<(), String> {
    if let Some(mut rx) = state.ssh_executor.get_task_log_stream(task_id) {
        tokio::spawn(async move {
            while let Ok(log_entry) = rx.recv().await {
                let _ = window.emit("task_log", &log_entry);
            }
        });
    }
    Ok(())
}

#[tauri::command]
pub async fn get_app_data_dir() -> Result<String, String> {
    let home_dir = std::env::var("HOME")
        .or_else(|_| std::env::var("USERPROFILE"))
        .map_err(|_| "Failed to get home directory")?;
    let data_dir = format!("{}/.local/share/sshmanger", home_dir);
    Ok(data_dir)
}

// Ping service commands
#[tauri::command]
pub async fn get_host_status(state: State<'_, AppState>, host_id: i64) -> Result<Option<HostStatus>, String> {
    Ok(state.ping_service.get_host_status(host_id))
}

#[tauri::command]
pub async fn get_all_host_statuses(state: State<'_, AppState>) -> Result<std::collections::HashMap<i64, HostStatus>, String> {
    Ok(state.ping_service.get_all_statuses())
}

#[tauri::command]
pub async fn start_host_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    match state.db.get_all_hosts().await {
        Ok(hosts) => {
            state.ping_service.start_monitoring(hosts).await;
            Ok(())
        }
        Err(e) => Err(e.to_string()),
    }
}

// SSH Terminal commands
#[tauri::command]
pub async fn create_ssh_session(state: State<'_, AppState>, host_id: i64) -> Result<String, String> {
    println!("Creating SSH session for host_id: {}", host_id);
    match state.db.get_host_by_id(host_id).await {
        Ok(Some(host)) => {
            state.ssh_terminal_service.create_session(host).await
        }
        Ok(None) => Err("Host not found".to_string()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn send_ssh_command(state: State<'_, AppState>, session_id: String, command: String) -> Result<(), String> {
    state.ssh_terminal_service.send_command(&session_id, &command).await
}

#[tauri::command]
pub async fn close_ssh_session(state: State<'_, AppState>, session_id: String) -> Result<(), String> {
    state.ssh_terminal_service.close_session(&session_id);
    Ok(())
}

#[tauri::command]
pub async fn browse_remote_directory(state: State<'_, AppState>, host_id: i64, path: String) -> Result<Vec<serde_json::Value>, String> {
    match state.db.get_host_by_id(host_id).await {
        Ok(Some(host)) => {
            // 使用SSH执行ls命令获取目录内容
            let command = format!("ls -la '{}'", path);
            match crate::ssh_executor::SshExecutor::create_ssh_session(&host).await {
                Ok(session) => {
                    match session.channel_session() {
                        Ok(mut channel) => {
                            match channel.exec(&command) {
                                Ok(_) => {
                                    let mut output = String::new();
                                    match channel.read_to_string(&mut output) {
                                        Ok(_) => {
                                            channel.wait_close().ok();

                                            // 解析ls输出
                                            let mut files = Vec::new();
                                            for line in output.lines().skip(1) { // 跳过第一行 "total xxx"
                                                if let Some(file_info) = parse_ls_line(line, &path) {
                                                    files.push(file_info);
                                                }
                                            }
                                            Ok(files)
                                        }
                                        Err(e) => Err(format!("读取输出失败: {}", e)),
                                    }
                                }
                                Err(e) => Err(format!("执行命令失败: {}", e)),
                            }
                        }
                        Err(e) => Err(format!("创建通道失败: {}", e)),
                    }
                }
                Err(e) => Err(format!("SSH连接失败: {}", e)),
            }
        }
        Ok(None) => Err("Host not found".to_string()),
        Err(e) => Err(e.to_string()),
    }
}

// 解析ls -la输出的单行
fn parse_ls_line(line: &str, parent_path: &str) -> Option<serde_json::Value> {
    let parts: Vec<&str> = line.split_whitespace().collect();
    if parts.len() < 9 {
        return None;
    }

    let permissions = parts[0];
    let name = parts[8..].join(" ");

    // 跳过 . 和 .. 目录
    if name == "." || name == ".." {
        return None;
    }

    let is_directory = permissions.starts_with('d');
    let full_path = if parent_path.ends_with('/') {
        format!("{}{}", parent_path, name)
    } else {
        format!("{}/{}", parent_path, name)
    };

    Some(serde_json::json!({
        "title": name,
        "key": full_path,
        "isLeaf": !is_directory,
        "icon": if is_directory { "folder" } else { "file" },
        "permissions": permissions,
        "size": parts.get(4).unwrap_or(&"0"),
        "modified": parts[5..8].join(" ")
    }))
}

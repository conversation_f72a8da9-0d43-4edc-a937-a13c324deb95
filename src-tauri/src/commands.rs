use std::sync::Arc;
use tauri::{State, Emitter};
use crate::database::Database;
use crate::ssh_executor::SshExecutor;
use crate::models::{Host, Task, CreateHostRequest, UpdateHostRequest, CreateTaskRequest, LogEntry};

// Application state
pub struct AppState {
    pub db: Arc<Database>,
    pub ssh_executor: Arc<SshExecutor>,
}

// Host management commands
#[tauri::command]
pub async fn get_hosts(state: State<'_, AppState>) -> Result<Vec<Host>, String> {
    state.db.get_all_hosts().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_host(state: State<'_, AppState>, id: i64) -> Result<Option<Host>, String> {
    state.db.get_host_by_id(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_host(state: State<'_, AppState>, request: CreateHostRequest) -> Result<Host, String> {
    state.db.create_host(request).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_host(state: State<'_, AppState>, request: UpdateHostRequest) -> Result<Host, String> {
    state.db.update_host(request).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_host(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    state.db.delete_host(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn test_host_connection(state: State<'_, AppState>, id: i64) -> Result<bool, String> {
    let host = state.db.get_host_by_id(id).await.map_err(|e| e.to_string())?;
    if let Some(host) = host {
        // Create a simple test task
        let test_command = "echo 'Connection test successful'";
        match crate::ssh_executor::SshExecutor::create_ssh_session(&host).await {
            Ok(session) => {
                match session.channel_session() {
                    Ok(mut channel) => {
                        match channel.exec(test_command) {
                            Ok(_) => {
                                let _ = channel.wait_close();
                                Ok(true)
                            },
                            Err(_) => Ok(false)
                        }
                    },
                    Err(_) => Ok(false)
                }
            },
            Err(_) => Ok(false)
        }
    } else {
        Err("Host not found".to_string())
    }
}

// Task management commands
#[tauri::command]
pub async fn get_tasks(state: State<'_, AppState>) -> Result<Vec<Task>, String> {
    state.db.get_all_tasks().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_task(state: State<'_, AppState>, id: i64) -> Result<Option<Task>, String> {
    state.db.get_task_by_id(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn create_task(state: State<'_, AppState>, request: CreateTaskRequest) -> Result<Task, String> {
    state.db.create_task(request).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn delete_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    state.db.delete_task(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn execute_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    let executor = state.ssh_executor.clone();
    tokio::spawn(async move {
        if let Err(e) = executor.execute_task(id).await {
            eprintln!("Task execution failed: {}", e);
        }
    });
    Ok(())
}

#[tauri::command]
pub async fn cancel_task(state: State<'_, AppState>, id: i64) -> Result<(), String> {
    state.ssh_executor.cancel_task(id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn is_task_running(state: State<'_, AppState>, id: i64) -> Result<bool, String> {
    Ok(state.ssh_executor.is_task_running(id))
}

// Log management commands
#[tauri::command]
pub async fn get_task_logs(state: State<'_, AppState>, task_id: i64) -> Result<Vec<LogEntry>, String> {
    state.db.get_task_logs(task_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn clear_task_logs(state: State<'_, AppState>, task_id: i64) -> Result<(), String> {
    state.db.clear_task_logs(task_id).await.map_err(|e| e.to_string())
}

// Real-time log streaming
#[tauri::command]
pub async fn subscribe_task_logs(
    state: State<'_, AppState>,
    task_id: i64,
    window: tauri::Window,
) -> Result<(), String> {
    if let Some(mut rx) = state.ssh_executor.get_task_log_stream(task_id) {
        tokio::spawn(async move {
            while let Ok(log_entry) = rx.recv().await {
                let _ = window.emit("task_log", &log_entry);
            }
        });
    }
    Ok(())
}

#[tauri::command]
pub async fn get_app_data_dir() -> Result<String, String> {
    let home_dir = std::env::var("HOME")
        .or_else(|_| std::env::var("USERPROFILE"))
        .map_err(|_| "Failed to get home directory")?;
    let data_dir = format!("{}/.local/share/sshmanger", home_dir);
    Ok(data_dir)
}

{"name": "s<PERSON><PERSON><PERSON>", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@tauri-apps/api": "^2.6.0", "antd": "^5.26.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3", "ssh2": "^1.15.0"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/ssh2": "^1.11.15", "@vitejs/plugin-react": "^4.0.3", "typescript": "^5.0.2", "vite": "^4.4.4"}}
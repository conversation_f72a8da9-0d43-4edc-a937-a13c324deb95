import React from 'react';
import { Layout, Menu } from 'antd';
import { DesktopOutlined, UnorderedListOutlined, SettingOutlined, AppstoreOutlined } from '@ant-design/icons';
import { BrowserRouter as Router, Route, Routes, Link, Navigate } from 'react-router-dom';
import HostManagement from './pages/HostManagement';
import TaskManagement from './pages/TaskManagement';
import Settings from './pages/Settings';
import { LanguageProvider, useLanguage } from './contexts/LanguageContext';
import './App.css';

const { Header, Content, Sider } = Layout;

const AppContent: React.FC = () => {
  const { t } = useLanguage();

  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider collapsible>
          <div className="logo" style={{
            height: 32,
            margin: 16,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>
            <AppstoreOutlined style={{ marginRight: 8 }} />
            Tasks
          </div>
          <Menu theme="dark" mode="inline" defaultSelectedKeys={['1']}>
            <Menu.Item key="1" icon={<DesktopOutlined />}>
              <Link to="/hosts">{t('nav.hostManagement')}</Link>
            </Menu.Item>
            <Menu.Item key="2" icon={<UnorderedListOutlined />}>
              <Link to="/tasks">{t('nav.taskManagement')}</Link>
            </Menu.Item>
            <Menu.Item key="3" icon={<SettingOutlined />}>
              <Link to="/settings">{t('nav.settings')}</Link>
            </Menu.Item>
          </Menu>
        </Sider>
        <Layout className="site-layout">
          <Content style={{ margin: '16px', padding: 24, background: '#fff' }}>
            <Routes>
              <Route path="/" element={<Navigate to="/hosts" replace />} />
              <Route path="/hosts" element={<HostManagement />} />
              <Route path="/tasks" element={<TaskManagement />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
};

function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}

export default App;

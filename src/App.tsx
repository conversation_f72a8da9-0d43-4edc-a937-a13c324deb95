import React from 'react';
import { Layout, Menu } from 'antd';
import { DesktopOutlined, FileOutlined, SettingOutlined, AppstoreOutlined } from '@ant-design/icons';
import { BrowserRouter as Router, Route, Routes, Link, Navigate } from 'react-router-dom';
import HostManagement from './pages/HostManagement';
import TaskManagement from './pages/TaskManagement';
import Settings from './pages/Settings';
import './App.css';

const { Header, Content, Sider } = Layout;

function App() {
  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider collapsible>
          <div className="logo" style={{
            height: 32,
            margin: 16,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '18px',
            fontWeight: 'bold'
          }}>
            <AppstoreOutlined style={{ marginRight: 8 }} />
            Tasks
          </div>
          <Menu theme="dark" mode="inline" defaultSelectedKeys={['1']}>
            <Menu.Item key="1" icon={<DesktopOutlined />}>
              <Link to="/hosts">Host Management</Link>
            </Menu.Item>
            <Menu.Item key="2" icon={<FileOutlined />}>
              <Link to="/tasks">Task Management</Link>
            </Menu.Item>
            <Menu.Item key="3" icon={<SettingOutlined />}>
              <Link to="/settings">Settings</Link>
            </Menu.Item>
          </Menu>
        </Sider>
        <Layout className="site-layout">
          <Header style={{ padding: 0, background: '#fff' }}>
            <h1 style={{ margin: 0, padding: '0 24px', lineHeight: '64px' }}>SSH Manager</h1>
          </Header>
          <Content style={{ margin: '24px 16px', padding: 24, background: '#fff' }}>
            <Routes>
              <Route path="/" element={<Navigate to="/hosts" replace />} />
              <Route path="/hosts" element={<HostManagement />} />
              <Route path="/tasks" element={<TaskManagement />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
}

export default App;

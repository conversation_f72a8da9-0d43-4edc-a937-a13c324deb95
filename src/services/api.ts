import { invoke } from '@tauri-apps/api/core';
import { Host, CreateHostRequest, UpdateHostRequest, Task, CreateTaskRequest, LogEntry } from '../types';

// Host management APIs
export const hostApi = {
  getHosts: (): Promise<Host[]> => invoke('get_hosts'),
  getHost: (id: number): Promise<Host | null> => invoke('get_host', { id }),
  createHost: (request: CreateHostRequest): Promise<Host> => invoke('create_host', { request }),
  createHostsBatch: (requests: CreateHostRequest[]): Promise<Host[]> => invoke('create_hosts_batch', { requests }),
  updateHost: (request: UpdateHostRequest): Promise<Host> => invoke('update_host', { request }),
  deleteHost: (id: number): Promise<void> => invoke('delete_host', { id }),
  deleteHostsBatch: (ids: number[]): Promise<number[]> => invoke('delete_hosts_batch', { ids }),
  testConnection: (id: number): Promise<boolean> => invoke('test_host_connection', { id }),
};

// Task management APIs
export const taskApi = {
  getTasks: (): Promise<Task[]> => invoke('get_tasks'),
  getTask: (id: number): Promise<Task | null> => invoke('get_task', { id }),
  createTask: (request: CreateTaskRequest): Promise<Task> => invoke('create_task', { request }),
  deleteTask: (id: number): Promise<void> => invoke('delete_task', { id }),
  executeTask: (id: number): Promise<void> => invoke('execute_task', { id }),
  cancelTask: (id: number): Promise<void> => invoke('cancel_task', { id }),
  isTaskRunning: (id: number): Promise<boolean> => invoke('is_task_running', { id }),
};

// Log management APIs
export const logApi = {
  getTaskLogs: (taskId: number): Promise<LogEntry[]> => invoke('get_task_logs', { task_id: taskId }),
  clearTaskLogs: (taskId: number): Promise<void> => invoke('clear_task_logs', { task_id: taskId }),
  subscribeTaskLogs: (taskId: number): Promise<void> => invoke('subscribe_task_logs', { task_id: taskId }),
};

// Utility APIs
export const utilApi = {
  getAppDataDir: (): Promise<string> => invoke('get_app_data_dir'),
};

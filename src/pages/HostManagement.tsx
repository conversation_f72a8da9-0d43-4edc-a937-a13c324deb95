import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  message, 
  Popconfirm,
  Tag,
  Space,
  Tooltip
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import { Host, CreateHostRequest, UpdateHostRequest } from '../types';
import { hostApi } from '../services/api';

const { TextArea } = Input;

const HostManagement: React.FC = () => {
  const [hosts, setHosts] = useState<Host[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingHost, setEditingHost] = useState<Host | null>(null);
  const [testingConnection, setTestingConnection] = useState<number | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadHosts();
  }, []);

  const loadHosts = async () => {
    setLoading(true);
    try {
      const hostList = await hostApi.getHosts();
      setHosts(hostList);
    } catch (error) {
      message.error('Failed to load hosts');
      console.error('Error loading hosts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateHost = () => {
    setEditingHost(null);
    form.resetFields();
    form.setFieldsValue({ port: 22 });
    setModalVisible(true);
  };

  const handleEditHost = (host: Host) => {
    setEditingHost(host);
    form.setFieldsValue(host);
    setModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingHost) {
        const updateRequest: UpdateHostRequest = {
          id: editingHost.id,
          ...values,
        };
        await hostApi.updateHost(updateRequest);
        message.success('Host updated successfully');
      } else {
        const createRequest: CreateHostRequest = values;
        await hostApi.createHost(createRequest);
        message.success('Host created successfully');
      }
      setModalVisible(false);
      loadHosts();
    } catch (error) {
      message.error(editingHost ? 'Failed to update host' : 'Failed to create host');
      console.error('Error saving host:', error);
    }
  };

  const handleDeleteHost = async (hostId: number) => {
    try {
      await hostApi.deleteHost(hostId);
      message.success('Host deleted successfully');
      loadHosts();
    } catch (error) {
      message.error('Failed to delete host');
      console.error('Error deleting host:', error);
    }
  };

  const handleTestConnection = async (hostId: number) => {
    setTestingConnection(hostId);
    try {
      const isConnected = await hostApi.testConnection(hostId);
      if (isConnected) {
        message.success('Connection test successful');
      } else {
        message.error('Connection test failed');
      }
    } catch (error) {
      message.error('Connection test failed');
      console.error('Error testing connection:', error);
    } finally {
      setTestingConnection(null);
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Hostname',
      dataIndex: 'hostname',
      key: 'hostname',
    },
    {
      title: 'Port',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'Auth Method',
      key: 'auth_method',
      render: (record: Host) => (
        <Tag color={record.private_key ? 'blue' : 'green'}>
          {record.private_key ? 'Private Key' : 'Password'}
        </Tag>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Host) => (
        <Space>
          <Tooltip title="Test Connection">
            <Button
              icon={<LinkOutlined />}
              size="small"
              loading={testingConnection === record.id}
              onClick={() => handleTestConnection(record.id)}
            />
          </Tooltip>
          <Button
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditHost(record)}
          />
          <Popconfirm
            title="Are you sure you want to delete this host?"
            onConfirm={() => handleDeleteHost(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button icon={<DeleteOutlined />} size="small" danger />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateHost}
        >
          Add Host
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={hosts}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      <Modal
        title={editingHost ? 'Edit Host' : 'Add Host'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Name"
            rules={[{ required: true, message: 'Please enter host name' }]}
          >
            <Input placeholder="Enter host name" />
          </Form.Item>

          <Form.Item
            name="hostname"
            label="Hostname/IP"
            rules={[{ required: true, message: 'Please enter hostname or IP' }]}
          >
            <Input placeholder="Enter hostname or IP address" />
          </Form.Item>

          <Form.Item
            name="port"
            label="Port"
            rules={[{ required: true, message: 'Please enter port' }]}
          >
            <InputNumber min={1} max={65535} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="username"
            label="Username"
            rules={[{ required: true, message: 'Please enter username' }]}
          >
            <Input placeholder="Enter username" />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
          >
            <Input.Password placeholder="Enter password (optional if using private key)" />
          </Form.Item>

          <Form.Item
            name="private_key"
            label="Private Key"
          >
            <TextArea
              rows={4}
              placeholder="Enter private key (optional if using password)"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={2} placeholder="Enter description (optional)" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default HostManagement;

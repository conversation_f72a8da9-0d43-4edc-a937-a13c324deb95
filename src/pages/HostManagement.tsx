import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Space,
  Tooltip
} from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, LinkOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { Host, CreateHostRequest, UpdateHostRequest, HostStatus } from '../types';
import { hostApi, pingApi } from '../services/api';
import { useLanguage } from '../contexts/LanguageContext';
import { createCustomPagination } from '../components/CustomPagination';
import SshTerminal from '../components/SshTerminal';

const { TextArea } = Input;

const HostManagement: React.FC = () => {
  const { t } = useLanguage();
  const [hosts, setHosts] = useState<Host[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingHost, setEditingHost] = useState<Host | null>(null);
  const [testingConnection, setTestingConnection] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [hostStatuses, setHostStatuses] = useState<Record<number, HostStatus>>({});
  const [sshTerminalVisible, setSshTerminalVisible] = useState(false);
  const [selectedHostForTerminal, setSelectedHostForTerminal] = useState<Host | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadHosts();
  }, []);

  // 定时刷新主机状态（每30秒）
  useEffect(() => {
    const interval = setInterval(() => {
      fetchHostStatuses();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadHosts = async () => {
    setLoading(true);
    try {
      const hostList = await hostApi.getHosts();
      setHosts(hostList);
      // 启动主机监控
      await startHostMonitoring();
    } catch (error) {
      message.error('Failed to load hosts');
      console.error('Error loading hosts:', error);
    } finally {
      setLoading(false);
    }
  };

  // 启动主机状态监控
  const startHostMonitoring = async () => {
    try {
      await pingApi.startHostMonitoring();
      // 立即获取一次状态
      await fetchHostStatuses();
    } catch (error) {
      console.error('Failed to start host monitoring:', error);
    }
  };

  // 获取主机状态
  const fetchHostStatuses = async () => {
    try {
      const statuses = await pingApi.getAllHostStatuses();
      setHostStatuses(statuses);
    } catch (error) {
      console.error('Failed to fetch host statuses:', error);
    }
  };

  // 打开SSH终端
  const openSshTerminal = (host: Host) => {
    setSelectedHostForTerminal(host);
    setSshTerminalVisible(true);
  };

  // 关闭SSH终端
  const closeSshTerminal = () => {
    setSshTerminalVisible(false);
    setSelectedHostForTerminal(null);
  };

  const handleCreateHost = () => {
    setEditingHost(null);
    form.resetFields();
    form.setFieldsValue({ port: 22 });
    setModalVisible(true);
  };

  const handleEditHost = (host: Host) => {
    setEditingHost(host);
    form.setFieldsValue(host);
    setModalVisible(true);
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingHost) {
        const updateRequest: UpdateHostRequest = {
          id: editingHost.id,
          ...values,
        };
        await hostApi.updateHost(updateRequest);
        message.success('Host updated successfully');
      } else {
        const createRequest: CreateHostRequest = values;
        await hostApi.createHost(createRequest);
        message.success('Host created successfully');
      }
      setModalVisible(false);
      loadHosts();
    } catch (error) {
      message.error(editingHost ? 'Failed to update host' : 'Failed to create host');
      console.error('Error saving host:', error);
    }
  };

  const handleDeleteHost = async (hostId: number) => {
    try {
      await hostApi.deleteHost(hostId);
      message.success('Host deleted successfully');
      loadHosts();
    } catch (error) {
      message.error('Failed to delete host');
      console.error('Error deleting host:', error);
    }
  };

  const handleTestConnection = async (hostId: number) => {
    setTestingConnection(hostId);
    try {
      const isConnected = await hostApi.testConnection(hostId);
      if (isConnected) {
        message.success('Connection test successful');
      } else {
        message.error('Connection test failed');
      }
    } catch (error) {
      message.error('Connection test failed');
      console.error('Error testing connection:', error);
    } finally {
      setTestingConnection(null);
    }
  };

  const handleExportCSV = () => {
    const csvHeaders = [
      'Name',
      'Hostname',
      'Port',
      'Username',
      'Password',
      'Private Key',
      'Description'
    ];

    const csvData = hosts.map(host => [
      host.name,
      host.hostname,
      host.port.toString(),
      host.username,
      host.password || '',
      host.private_key || '',
      host.description || ''
    ]);

    const csvContent = [csvHeaders, ...csvData]
      .map(row => row.map(field => `"${field.replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `ssh_hosts_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success(t('host.exportSuccess'));
  };

  const handleImportCSV = (file: File) => {
    // 检查文件大小（限制为5MB）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      message.error(t('host.fileTooLarge'));
      return false;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;

        // 检查CSV内容长度
        if (csv.length > maxSize) {
          message.error(t('host.fileTooLarge'));
          return;
        }

        const lines = csv.split('\n').filter(line => line.trim());

        // 检查行数限制（最多1000行）
        if (lines.length > 1000) {
          message.error(t('host.tooManyRows'));
          return;
        }

        if (lines.length < 2) {
          message.error(t('host.noValidData'));
          return;
        }

        const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());
        const importedHosts: CreateHostRequest[] = [];

        for (let i = 1; i < lines.length; i++) {
          try {
            const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
            if (values.length >= 4) {
              const port = parseInt(values[2]);

              // 验证端口号
              if (isNaN(port) || port < 1 || port > 65535) {
                console.warn(`Invalid port on line ${i + 1}: ${values[2]}`);
                continue;
              }

              const hostData: CreateHostRequest = {
                name: values[0] || `Host_${i}`,
                hostname: values[1] || '',
                port: port,
                username: values[3] || '',
                password: values[4] || undefined,
                private_key: values[5] || undefined,
                description: values[6] || undefined,
              };

              // 验证必填字段
              if (hostData.hostname && hostData.username && hostData.name) {
                importedHosts.push(hostData);
              } else {
                console.warn(`Invalid data on line ${i + 1}: missing required fields`);
              }
            }
          } catch (lineError) {
            console.warn(`Error parsing line ${i + 1}:`, lineError);
            continue;
          }
        }

        if (importedHosts.length > 0) {
          importHostsBatch(importedHosts);
        } else {
          message.error(t('host.noValidData'));
        }
      } catch (error) {
        message.error(t('host.parseError'));
        console.error('CSV import error:', error);
      }
    };

    reader.onerror = () => {
      message.error(t('host.fileReadError'));
    };

    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  const importHostsBatch = async (hostList: CreateHostRequest[]) => {
    const maxBatchSize = 20; // 每批最多20个主机
    let totalSuccess = 0;
    let totalError = 0;

    // 显示进度提示
    const hideLoading = message.loading(t('host.importing'), 0);

    try {
      // 分批处理，避免一次性处理过多数据
      for (let i = 0; i < hostList.length; i += maxBatchSize) {
        const batch = hostList.slice(i, i + maxBatchSize);

        try {
          // 使用真正的批量API
          const createdHosts = await hostApi.createHostsBatch(batch);
          totalSuccess += createdHosts.length;

          // 如果创建的数量少于批次大小，说明有部分失败
          const failedCount = batch.length - createdHosts.length;
          if (failedCount > 0) {
            totalError += failedCount;
            console.warn(`Batch ${Math.floor(i/maxBatchSize) + 1}: ${failedCount} hosts failed to import`);
          }

        } catch (error) {
          console.error('Batch import error:', error);
          totalError += batch.length;
        }

        // 更新进度
        const progress = Math.round(((i + batch.length) / hostList.length) * 100);
        console.log(`Import progress: ${progress}% (${totalSuccess + totalError}/${hostList.length})`);

        // 批次间短暂延迟，让系统有时间处理
        if (i + maxBatchSize < hostList.length) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    } catch (error) {
      console.error('Import process error:', error);
      message.error(t('host.importError'));
    } finally {
      hideLoading();
    }

    // 显示结果
    if (totalSuccess > 0) {
      message.success(t('host.importSuccess') + ` (${totalSuccess})`);
      loadHosts(); // 重新加载主机列表
    }

    if (totalError > 0) {
      message.warning(t('host.importError') + ` (${totalError})`);
    }

    if (totalSuccess === 0 && totalError === 0) {
      message.info(t('host.noValidData'));
    }
  };

  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(t('host.selectHostsToDelete'));
      return;
    }

    // 限制批量删除数量
    if (selectedRowKeys.length > 50) {
      message.error(t('host.tooManySelected'));
      return;
    }

    // 显示进度提示
    const hideLoading = message.loading(t('host.deleting'), 0);

    try {
      const hostIds = selectedRowKeys.map(id => Number(id));

      // 使用真正的批量删除API
      const deletedIds = await hostApi.deleteHostsBatch(hostIds);

      const successCount = deletedIds.length;
      const errorCount = hostIds.length - successCount;

      // 显示结果
      if (successCount > 0) {
        message.success(t('host.batchDeleteSuccess') + ` (${successCount})`);
        setSelectedRowKeys([]);
        loadHosts(); // 重新加载主机列表
      }

      if (errorCount > 0) {
        message.warning(t('host.batchDeleteError') + ` (${errorCount})`);
      }

      if (successCount === 0) {
        message.info(t('host.noHostsToDelete'));
      }

    } catch (error) {
      console.error('Batch delete error:', error);
      message.error(t('host.batchDeleteError'));
    } finally {
      hideLoading();
    }
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    onSelectAll: (selected: boolean, selectedRows: Host[], changeRows: Host[]) => {
      if (selected) {
        setSelectedRowKeys(hosts.map(host => host.id));
      } else {
        setSelectedRowKeys([]);
      }
    },
  };

  const columns = [
    {
      title: t('host.name'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: t('host.hostname'),
      dataIndex: 'hostname',
      key: 'hostname',
      width: 180,
    },
    {
      title: t('host.port'),
      dataIndex: 'port',
      key: 'port',
      width: 80,
      align: 'left' as const,
    },
    {
      title: t('host.username'),
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: t('host.authMethod'),
      key: 'auth_method',
      width: 120,
      align: 'left' as const,
      render: (record: Host) => (
        <Tag color={record.private_key ? 'blue' : 'green'}>
          {record.private_key ? t('host.privateKey') : t('host.password')}
        </Tag>
      ),
    },
    {
      title: t('host.description'),
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => text || '-',
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      align: 'left' as const,
      render: (record: Host) => {
        const status = hostStatuses[record.id];
        if (!status) {
          return <Tag color="default">检测中</Tag>;
        }

        const getStatusColor = (status: string) => {
          switch (status) {
            case 'online': return 'success';
            case 'offline': return 'error';
            case 'checking': return 'processing';
            default: return 'default';
          }
        };

        const getStatusText = (status: string, pingTime?: number) => {
          switch (status) {
            case 'online': return pingTime ? `在线 ${pingTime.toFixed(0)}ms` : '在线';
            case 'offline': return '离线';
            case 'checking': return '检测中';
            default: return '未知';
          }
        };

        return (
          <Tag color={getStatusColor(status.status)}>
            {getStatusText(status.status, status.ping_time)}
          </Tag>
        );
      },
    },
    {
      title: t('host.created'),
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      align: 'left' as const,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: t('host.actions'),
      key: 'actions',
      width: 200,
      align: 'left' as const,
      render: (record: Host) => (
        <Space>
          <Tooltip title="SSH Terminal">
            <Button
              icon={<span style={{ fontFamily: 'monospace' }}>$</span>}
              size="small"
              onClick={() => openSshTerminal(record)}
            />
          </Tooltip>
          <Tooltip title={t('host.testConnection')}>
            <Button
              icon={<LinkOutlined />}
              size="small"
              loading={testingConnection === record.id}
              onClick={() => handleTestConnection(record.id)}
            />
          </Tooltip>
          <Tooltip title={t('host.edit')}>
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditHost(record)}
            />
          </Tooltip>
          <Popconfirm
            title={t('host.deleteConfirm')}
            onConfirm={() => handleDeleteHost(record.id)}
            okText={t('common.yes')}
            cancelText={t('common.no')}
          >
            <Tooltip title={t('host.delete')}>
              <Button icon={<DeleteOutlined />} size="small" danger />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', gap: 8 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateHost}
        >
          {t('host.addHost')}
        </Button>

        <Button
          icon={<DownloadOutlined />}
          onClick={handleExportCSV}
          disabled={hosts.length === 0}
        >
          {t('host.exportCSV')}
        </Button>

        <Button
          icon={<UploadOutlined />}
          onClick={() => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv';
            input.onchange = (e) => {
              const file = (e.target as HTMLInputElement).files?.[0];
              if (file) {
                handleImportCSV(file);
              }
            };
            input.click();
          }}
        >
          {t('host.importCSV')}
        </Button>

        <Popconfirm
          title={t('host.batchDeleteConfirm')}
          description={`${t('host.selectedCount')}: ${selectedRowKeys.length}`}
          onConfirm={handleBatchDelete}
          okText={t('common.yes')}
          cancelText={t('common.no')}
          disabled={selectedRowKeys.length === 0}
        >
          <Button
            icon={<DeleteOutlined />}
            danger
            disabled={selectedRowKeys.length === 0}
          >
            {t('host.batchDelete')} ({selectedRowKeys.length})
          </Button>
        </Popconfirm>
      </div>

      <Table
        columns={columns}
        dataSource={hosts}
        rowKey="id"
        loading={loading}
        pagination={createCustomPagination({
          total: hosts.length,
          current: currentPage,
          pageSize: pageSize,
          onChange: (page, size) => {
            setCurrentPage(page);
            if (size) {
              setPageSize(size);
            }
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(1); // 重置到第一页
            setPageSize(size);
          },
        })}
        rowSelection={rowSelection}
      />

      <Modal
        title={editingHost ? t('host.editHost') : t('host.addHost')}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
        okText={t('common.save')}
        cancelText={t('common.cancel')}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label={t('host.name')}
            rules={[{ required: true, message: t('host.nameRequired') }]}
          >
            <Input placeholder={t('host.namePlaceholder')} />
          </Form.Item>

          <Form.Item
            name="hostname"
            label={t('host.hostname')}
            rules={[{ required: true, message: t('host.hostnameRequired') }]}
          >
            <Input placeholder={t('host.hostnamePlaceholder')} />
          </Form.Item>

          <Form.Item
            name="port"
            label={t('host.port')}
            rules={[{ required: true, message: t('host.portRequired') }]}
          >
            <InputNumber min={1} max={65535} style={{ width: '100%' }} placeholder="22" />
          </Form.Item>

          <Form.Item
            name="username"
            label={t('host.username')}
            rules={[{ required: true, message: t('host.usernameRequired') }]}
          >
            <Input placeholder={t('host.usernamePlaceholder')} />
          </Form.Item>

          <Form.Item
            name="password"
            label={t('host.password')}
          >
            <Input.Password placeholder={t('host.passwordPlaceholder')} />
          </Form.Item>

          <Form.Item
            name="private_key"
            label={t('host.privateKey')}
          >
            <TextArea
              rows={4}
              placeholder={t('host.privateKeyPlaceholder')}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label={t('host.description')}
          >
            <TextArea rows={2} placeholder={t('host.descriptionPlaceholder')} />
          </Form.Item>
        </Form>
      </Modal>

      {/* SSH Terminal */}
      <SshTerminal
        visible={sshTerminalVisible}
        host={selectedHostForTerminal}
        onClose={closeSshTerminal}
      />
    </div>
  );
};

export default HostManagement;

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Input, Button, Typography, Space, message } from 'antd';
import { CloseOutlined, SendOutlined } from '@ant-design/icons';
import { Host, TerminalOutput } from '../types';
import { sshTerminalApi } from '../services/api';
import { listen } from '@tauri-apps/api/event';

const { Text } = Typography;

interface SshTerminalProps {
  visible: boolean;
  host: Host | null;
  onClose: () => void;
}

const SshTerminal: React.FC<SshTerminalProps> = ({ visible, host, onClose }) => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [output, setOutput] = useState<string>('');
  const [command, setCommand] = useState<string>('');
  const [connecting, setConnecting] = useState(false);
  const [connected, setConnected] = useState(false);
  const outputRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [output]);

  // 创建SSH会话
  const createSession = async () => {
    if (!host) return;

    setConnecting(true);
    setOutput('正在连接...\n');

    try {
      const newSessionId = await sshTerminalApi.createSession(host.id);
      setSessionId(newSessionId);
      setConnected(true);

      // 模拟连接成功的输出
      setTimeout(() => {
        setOutput(prev => prev + `已连接到 ${host.username}@${host.hostname}:${host.port}\n`);
        setOutput(prev => prev + `欢迎使用SSH终端模拟器\n`);
        setOutput(prev => prev + `支持的命令: ls, pwd, whoami, date, echo, help\n`);
        setOutput(prev => prev + `${host.username}@${host.hostname}:~$ `);
      }, 500);

      message.success('SSH连接成功');
    } catch (error) {
      setOutput(prev => prev + `连接失败: ${error}\n`);
      message.error('SSH连接失败');
    } finally {
      setConnecting(false);
    }
  };

  // 发送命令
  const sendCommand = async () => {
    if (!sessionId || !command.trim()) return;

    try {
      // 显示命令
      setOutput(prev => prev + `${command}\n`);

      // 发送命令到后端
      await sshTerminalApi.sendCommand(sessionId, command);

      // 模拟命令输出
      setTimeout(() => {
        const response = executeLocalCommand(command);
        setOutput(prev => prev + response);
        if (host) {
          setOutput(prev => prev + `${host.username}@${host.hostname}:~$ `);
        }
      }, 200);

      setCommand('');
    } catch (error) {
      setOutput(prev => prev + `命令执行失败: ${error}\n`);
      message.error('命令执行失败');
    }
  };

  // 本地模拟命令执行
  const executeLocalCommand = (cmd: string): string => {
    const trimmedCmd = cmd.trim();

    switch (trimmedCmd) {
      case 'ls':
        return 'Desktop  Documents  Downloads  Pictures  Videos\n';
      case 'pwd':
        return '/home/<USER>';
      case 'whoami':
        return 'user\n';
      case 'date':
        return new Date().toString() + '\n';
      case 'help':
        return '支持的命令:\n  ls      - 列出文件\n  pwd     - 显示当前目录\n  whoami  - 显示用户名\n  date    - 显示日期时间\n  echo    - 输出文本\n  help    - 显示帮助\n';
      case '':
        return '';
      default:
        if (trimmedCmd.startsWith('echo ')) {
          return trimmedCmd.substring(5) + '\n';
        }
        return `bash: ${trimmedCmd}: command not found\n`;
    }
  };

  // 关闭会话
  const closeSession = async () => {
    if (sessionId) {
      try {
        await sshTerminalApi.closeSession(sessionId);
      } catch (error) {
        console.error('关闭SSH会话失败:', error);
      }
    }
    
    setSessionId(null);
    setConnected(false);
    setOutput('');
    setCommand('');
    onClose();
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      sendCommand();
    }
  };

  // 当模态框打开时自动连接
  useEffect(() => {
    if (visible && host && !sessionId) {
      createSession();
    }
  }, [visible, host]);

  // 格式化输出文本，添加语法高亮
  const formatOutput = (text: string) => {
    return text.split('\n').map((line, index) => {
      let className = '';
      let style: React.CSSProperties = {};
      
      // 命令行提示符高亮
      if (line.startsWith('$ ')) {
        className = 'terminal-command';
        style = { color: '#00ff00', fontWeight: 'bold' };
      }
      // 错误信息高亮
      else if (line.includes('error') || line.includes('Error') || line.includes('失败')) {
        className = 'terminal-error';
        style = { color: '#ff4d4f' };
      }
      // 成功信息高亮
      else if (line.includes('success') || line.includes('Success') || line.includes('成功')) {
        className = 'terminal-success';
        style = { color: '#52c41a' };
      }
      // 警告信息高亮
      else if (line.includes('warning') || line.includes('Warning') || line.includes('警告')) {
        className = 'terminal-warning';
        style = { color: '#faad14' };
      }
      
      return (
        <div key={index} className={className} style={style}>
          {line || '\u00A0'} {/* 空行显示为不间断空格 */}
        </div>
      );
    });
  };

  return (
    <Modal
      title={
        <Space>
          <Text strong>SSH Terminal</Text>
          {host && (
            <Text type="secondary">
              {host.username}@{host.hostname}:{host.port}
            </Text>
          )}
        </Space>
      }
      open={visible}
      onCancel={closeSession}
      footer={null}
      width={800}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0 }}
      destroyOnClose
    >
      <div style={{ 
        height: '500px', 
        display: 'flex', 
        flexDirection: 'column',
        backgroundColor: '#1f1f1f',
        color: '#ffffff'
      }}>
        {/* 终端输出区域 */}
        <div
          ref={outputRef}
          style={{
            flex: 1,
            padding: '12px',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            fontSize: '13px',
            lineHeight: '1.4',
            backgroundColor: '#1f1f1f',
            color: '#ffffff',
            overflow: 'auto',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-all'
          }}
        >
          {formatOutput(output)}
        </div>
        
        {/* 命令输入区域 */}
        <div style={{
          padding: '12px',
          borderTop: '1px solid #333',
          backgroundColor: '#2d2d2d'
        }}>
          <Space.Compact style={{ width: '100%' }}>
            <Input
              value={command}
              onChange={(e) => setCommand(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={connected ? "输入命令..." : "正在连接..."}
              disabled={!connected || connecting}
              style={{
                backgroundColor: '#1f1f1f',
                color: '#ffffff',
                border: '1px solid #333'
              }}
              prefix={<Text style={{ color: '#00ff00' }}>$</Text>}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={sendCommand}
              disabled={!connected || !command.trim() || connecting}
            >
              发送
            </Button>
          </Space.Compact>
        </div>
        
        {/* 状态栏 */}
        <div style={{
          padding: '8px 12px',
          backgroundColor: '#333',
          borderTop: '1px solid #444',
          fontSize: '12px',
          color: '#999'
        }}>
          <Space split={<span>|</span>}>
            <Text style={{ color: connected ? '#52c41a' : '#faad14' }}>
              {connecting ? '连接中...' : connected ? '已连接' : '未连接'}
            </Text>
            {sessionId && (
              <Text style={{ color: '#999' }}>
                会话ID: {sessionId.substring(0, 8)}...
              </Text>
            )}
            <Text style={{ color: '#999' }}>
              按 Enter 发送命令
            </Text>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

export default SshTerminal;

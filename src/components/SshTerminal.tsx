import React, { useState, useEffect, useRef } from 'react';
import { Modal, Input, Button, Typography, Space, message, Tree, Tabs, Divider } from 'antd';
import {
  CloseOutlined,
  SendOutlined,
  FolderOutlined,
  FileOutlined,
  CodeOutlined,
  PlusOutlined,
  SettingOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { Host, TerminalOutput } from '../types';
import { sshTerminalApi } from '../services/api';

const { Text } = Typography;
const { TabPane } = Tabs;

interface SshTerminalProps {
  visible: boolean;
  host: Host | null;
  onClose: () => void;
}

interface TerminalTab {
  key: string;
  title: string;
  output: string;
  command: string;
  connected: boolean;
  sessionId: string | null;
}

interface FileNode {
  title: string;
  key: string;
  icon?: React.ReactNode;
  children?: FileNode[];
  isLeaf?: boolean;
}

const SshTerminal: React.FC<SshTerminalProps> = ({ visible, host, onClose }) => {
  const [tabs, setTabs] = useState<TerminalTab[]>([]);
  const [activeTabKey, setActiveTabKey] = useState<string>('');
  const [connecting, setConnecting] = useState(false);
  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const outputRef = useRef<HTMLDivElement>(null);

  // 初始化文件树
  useEffect(() => {
    if (visible && host) {
      loadRemoteFileTree('/home');
      createNewTab();
    }
  }, [visible, host]);

  // 自动滚动到底部
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [tabs, activeTabKey]);

  // 加载远程文件树
  const loadRemoteFileTree = async (path: string = '/home') => {
    if (!host) return;

    try {
      const files = await sshTerminalApi.browseDirectory(host.id, path);
      const treeData = convertToTreeData(files, path);
      setFileTree(treeData);
      setExpandedKeys([path]);
    } catch (error) {
      console.error('Failed to load remote directory:', error);
      message.error('加载远程目录失败');
      // 如果失败，显示基本的目录结构
      setFileTree([{
        title: 'home',
        key: '/home',
        icon: <FolderOutlined />,
        children: []
      }]);
    }
  };

  // 将API返回的文件列表转换为Tree组件需要的格式
  const convertToTreeData = (files: any[], parentPath: string): FileNode[] => {
    return files.map(file => ({
      title: file.title,
      key: file.key,
      icon: file.icon === 'folder' ? <FolderOutlined /> : <FileOutlined />,
      isLeaf: file.isLeaf,
      children: file.isLeaf ? undefined : []
    }));
  };

  // 创建新标签页
  const createNewTab = async () => {
    if (!host) return;

    const tabKey = `tab-${Date.now()}`;
    const newTab: TerminalTab = {
      key: tabKey,
      title: `${host.hostname}`,
      output: '正在连接...\n',
      command: '',
      connected: false,
      sessionId: null
    };

    setTabs(prev => [...prev, newTab]);
    setActiveTabKey(tabKey);

    // 创建SSH会话
    setConnecting(true);
    try {
      const newSessionId = await sshTerminalApi.createSession(host.id);

      // 更新标签页状态
      setTabs(prev => prev.map(tab =>
        tab.key === tabKey
          ? {
              ...tab,
              sessionId: newSessionId,
              connected: true,
              output: tab.output + `已连接到 ${host.username}@${host.hostname}:${host.port}\n` +
                     `欢迎使用SSH终端模拟器\n` +
                     `支持的命令: ls, pwd, whoami, date, echo, help, cd, cat\n` +
                     `${host.username}@${host.hostname}:~$ `
            }
          : tab
      ));

      message.success('SSH连接成功');
    } catch (error) {
      setTabs(prev => prev.map(tab =>
        tab.key === tabKey
          ? { ...tab, output: tab.output + `连接失败: ${error}\n` }
          : tab
      ));
      message.error('SSH连接失败');
    } finally {
      setConnecting(false);
    }
  };

  // 发送命令
  const sendCommand = async () => {
    const currentTab = tabs.find(tab => tab.key === activeTabKey);
    if (!currentTab || !currentTab.sessionId || !currentTab.command.trim()) return;

    try {
      const command = currentTab.command;

      // 更新当前标签页 - 显示命令
      setTabs(prev => prev.map(tab =>
        tab.key === activeTabKey
          ? {
              ...tab,
              output: tab.output + `${command}\n`,
              command: ''
            }
          : tab
      ));

      // 发送命令到后端（真正的SSH命令执行）
      await sshTerminalApi.sendCommand(currentTab.sessionId, command);

    } catch (error) {
      setTabs(prev => prev.map(tab =>
        tab.key === activeTabKey
          ? { ...tab, output: tab.output + `命令执行失败: ${error}\n` }
          : tab
      ));
      message.error('命令执行失败');
    }
  };



  // 关闭标签页
  const closeTab = async (targetKey: string) => {
    const targetTab = tabs.find(tab => tab.key === targetKey);
    if (targetTab?.sessionId) {
      try {
        await sshTerminalApi.closeSession(targetTab.sessionId);
      } catch (error) {
        console.error('关闭SSH会话失败:', error);
      }
    }

    const newTabs = tabs.filter(tab => tab.key !== targetKey);
    setTabs(newTabs);

    if (activeTabKey === targetKey) {
      if (newTabs.length > 0) {
        setActiveTabKey(newTabs[newTabs.length - 1].key);
      } else {
        onClose();
      }
    }
  };

  // 关闭所有会话
  const closeAllSessions = async () => {
    for (const tab of tabs) {
      if (tab.sessionId) {
        try {
          await sshTerminalApi.closeSession(tab.sessionId);
        } catch (error) {
          console.error('关闭SSH会话失败:', error);
        }
      }
    }

    setTabs([]);
    setActiveTabKey('');
    onClose();
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      sendCommand();
    }
  };

  // 更新当前标签页的命令
  const updateCurrentTabCommand = (value: string) => {
    setTabs(prev => prev.map(tab =>
      tab.key === activeTabKey
        ? { ...tab, command: value }
        : tab
    ));
  };

  // 获取当前标签页
  const getCurrentTab = () => {
    return tabs.find(tab => tab.key === activeTabKey);
  };

  // 文件树节点点击
  const onFileTreeSelect = (selectedKeys: React.Key[], info: any) => {
    if (info.node.isLeaf) {
      const currentTab = getCurrentTab();
      if (currentTab) {
        updateCurrentTabCommand(`cat ${info.node.key}`);
      }
    }
  };

  // 格式化输出文本，添加语法高亮
  const formatOutput = (text: string) => {
    return text.split('\n').map((line, index) => {
      let className = '';
      let style: React.CSSProperties = {};
      
      // 命令行提示符高亮
      if (line.startsWith('$ ')) {
        className = 'terminal-command';
        style = { color: '#00ff00', fontWeight: 'bold' };
      }
      // 错误信息高亮
      else if (line.includes('error') || line.includes('Error') || line.includes('失败')) {
        className = 'terminal-error';
        style = { color: '#ff4d4f' };
      }
      // 成功信息高亮
      else if (line.includes('success') || line.includes('Success') || line.includes('成功')) {
        className = 'terminal-success';
        style = { color: '#52c41a' };
      }
      // 警告信息高亮
      else if (line.includes('warning') || line.includes('Warning') || line.includes('警告')) {
        className = 'terminal-warning';
        style = { color: '#faad14' };
      }
      
      return (
        <div key={index} className={className} style={style}>
          {line || '\u00A0'} {/* 空行显示为不间断空格 */}
        </div>
      );
    });
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <CodeOutlined />
            <Text strong>SSH Terminal</Text>
            {host && (
              <Text type="secondary">
                {host.username}@{host.hostname}:{host.port}
              </Text>
            )}
          </Space>
          <Space>
            <Button
              size="small"
              icon={<PlusOutlined />}
              onClick={createNewTab}
              disabled={!host}
            >
              新建
            </Button>
            <Button
              size="small"
              icon={<SettingOutlined />}
            >
              设置
            </Button>
          </Space>
        </div>
      }
      open={visible}
      onCancel={closeAllSessions}
      footer={null}
      width={1200}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0, height: '700px' }}
      destroyOnClose
    >
      <div style={{
        height: '700px',
        display: 'flex',
        backgroundColor: '#1e1e1e',
        color: '#ffffff'
      }}>
        {/* 左侧文件树 */}
        <div style={{
          width: '250px',
          backgroundColor: '#252526',
          borderRight: '1px solid #3e3e42',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* 文件树标题 */}
          <div style={{
            padding: '8px 12px',
            backgroundColor: '#2d2d30',
            borderBottom: '1px solid #3e3e42',
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#cccccc'
          }}>
            <Space>
              <FolderOutlined />
              文件浏览器
            </Space>
          </div>

          {/* 文件树 */}
          <div style={{ flex: 1, padding: '8px', overflow: 'auto' }}>
            <Tree
              treeData={fileTree}
              expandedKeys={expandedKeys}
              onExpand={(keys) => setExpandedKeys(keys as string[])}
              onSelect={onFileTreeSelect}
              showIcon
              style={{
                backgroundColor: 'transparent',
                color: '#cccccc'
              }}
            />
          </div>
        </div>

        {/* 右侧终端区域 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* 标签页 */}
          <Tabs
            type="editable-card"
            activeKey={activeTabKey}
            onChange={setActiveTabKey}
            onEdit={(targetKey, action) => {
              if (action === 'remove') {
                closeTab(targetKey as string);
              }
            }}
            style={{
              backgroundColor: '#2d2d30',
              margin: 0,
              minHeight: 'auto'
            }}
            tabBarStyle={{
              backgroundColor: '#2d2d30',
              margin: 0,
              borderBottom: '1px solid #3e3e42'
            }}
          >
            {tabs.map(tab => (
              <TabPane
                tab={
                  <Space>
                    <CodeOutlined />
                    {tab.title}
                    {tab.connected && <span style={{ color: '#52c41a' }}>●</span>}
                  </Space>
                }
                key={tab.key}
                closable={tabs.length > 1}
              >
                {/* 终端输出区域 */}
                <div
                  ref={outputRef}
                  style={{
                    height: '580px',
                    padding: '12px',
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    fontSize: '13px',
                    lineHeight: '1.4',
                    backgroundColor: '#1e1e1e',
                    color: '#ffffff',
                    overflow: 'auto',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all'
                  }}
                >
                  {formatOutput(tab.output)}
                </div>
              </TabPane>
            ))}
          </Tabs>

          {/* 命令输入区域 */}
          <div style={{
            padding: '12px',
            borderTop: '1px solid #3e3e42',
            backgroundColor: '#2d2d30'
          }}>
            <Space.Compact style={{ width: '100%' }}>
              <Input
                value={getCurrentTab()?.command || ''}
                onChange={(e) => updateCurrentTabCommand(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={getCurrentTab()?.connected ? "输入命令..." : "正在连接..."}
                disabled={!getCurrentTab()?.connected || connecting}
                style={{
                  backgroundColor: '#1e1e1e',
                  color: '#ffffff',
                  border: '1px solid #3e3e42'
                }}
                prefix={<Text style={{ color: '#00ff00' }}>$</Text>}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={sendCommand}
                disabled={!getCurrentTab()?.connected || !getCurrentTab()?.command?.trim() || connecting}
              >
                发送
              </Button>
            </Space.Compact>
          </div>

          {/* 状态栏 */}
          <div style={{
            padding: '6px 12px',
            backgroundColor: '#007acc',
            fontSize: '12px',
            color: '#ffffff',
            display: 'flex',
            justifyContent: 'space-between'
          }}>
            <Space split={<span style={{ margin: '0 8px' }}>|</span>}>
              <Text style={{ color: '#ffffff' }}>
                {connecting ? '连接中...' : getCurrentTab()?.connected ? '已连接' : '未连接'}
              </Text>
              {getCurrentTab()?.sessionId && (
                <Text style={{ color: '#ffffff' }}>
                  会话: {getCurrentTab()?.sessionId?.substring(0, 8)}...
                </Text>
              )}
            </Space>
            <Text style={{ color: '#ffffff' }}>
              标签页: {tabs.length} | 按 Enter 发送命令
            </Text>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default SshTerminal;

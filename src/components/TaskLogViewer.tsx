import React, { useEffect, useState } from 'react';
import { List, Tag, message } from 'antd';
import { logApi } from '../services/api';
import { LogEntry } from '../types';

interface TaskLogViewerProps {
  taskId: number;
}

const TaskLogViewer: React.FC<TaskLogViewerProps> = ({ taskId }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadLogs();
  }, [taskId]);

  const loadLogs = async () => {
    setLoading(true);
    try {
      const taskLogs = await logApi.getTaskLogs(taskId);
      setLogs(taskLogs);
    } catch (error) {
      message.error('Failed to load logs');
      console.error('Error loading logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderLogItem = (log: LogEntry) => {
    const colorMap = {
      stdout: 'blue',
      stderr: 'red',
      info: 'green',
      error: 'volcano',
    };
    return (
      <List.Item>
        <Tag color={colorMap[log.log_type]}>{log.log_type.toUpperCase()}</Tag>
        <span>{log.content}</span>
      </List.Item>
    );
  };

  return (
    <List<LogEntry>
      loading={loading}
      dataSource={logs}
      renderItem={renderLogItem}
      rowKey="id"
    />
  );
};

export default TaskLogViewer;


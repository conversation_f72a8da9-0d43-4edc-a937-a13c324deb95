export interface Host {
  id: number;
  name: string;
  hostname: string;
  port: number;
  username: string;
  password?: string;
  private_key?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateHostRequest {
  name: string;
  hostname: string;
  port: number;
  username: string;
  password?: string;
  private_key?: string;
  description?: string;
}

export interface UpdateHostRequest extends CreateHostRequest {
  id: number;
}

export interface Task {
  id: number;
  name: string;
  task_type: 'command' | 'file_transfer' | 'script';
  command?: string;
  script_content?: string;
  source_path?: string;
  target_path?: string;
  host_ids: string; // JSON array of host IDs
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  parallel: boolean;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface CreateTaskRequest {
  name: string;
  task_type: 'command' | 'file_transfer' | 'script';
  command?: string;
  script_content?: string;
  source_path?: string;
  target_path?: string;
  host_ids: number[];
  parallel: boolean;
}

export interface LogEntry {
  id: number;
  task_id: number;
  host_id: number;
  host_name: string;
  log_type: 'stdout' | 'stderr' | 'info' | 'error';
  content: string;
  timestamp: string;
}

export interface TaskStatus {
  task_id: number;
  host_id: number;
  host_name: string;
  status: string;
  progress: number;
  current_step: string;
}

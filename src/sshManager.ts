import { createClient } from 'ssh2';

export class SSHManager {
  private connections = new Map<string, any>();

  connect(config: { id: string; host: string; port: number; username: string; password: string }) {
    const client = createClient();
    client.connect({
      host: config.host,
      port: config.port,
      username: config.username,
      password: config.password
    });
    
    this.connections.set(config.id, client);
    return client;
  }

  getConnection(id: string) {
    return this.connections.get(id);
  }
}